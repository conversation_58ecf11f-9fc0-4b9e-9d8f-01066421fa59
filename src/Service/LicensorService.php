<?php

declare(strict_types=1);

namespace App\Service;

use App\DTO\Licensee\LicenseeListFilter;
use App\Entity\Licensor;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class LicensorService
{

    private ?LicenseeListFilter $listFilter = null;

    public function __construct(
        private readonly TokenStorageInterface  $tokenStorage,
        private readonly ProfileService         $profileService,
        private readonly EntityToOptionsService $entityToOptionsService,
    ) {
    }

    public function find(): Licensor
    {
        /** @var \App\Entity\User|null $user */
        $user = $this->tokenStorage->getToken()?->getUser();
        if (!$user) {
            throw new \RuntimeException('User not authenticated');
        }

        $licensor = $this->profileService->findLicensorByUser($user);
        if (!$licensor) {
            throw new \RuntimeException('Licensor not found');
        }

        return $licensor;
    }

    public function getListFilter(): LicenseeListFilter
    {
        if ($this->listFilter) {
            return $this->listFilter;
        }

        $this->listFilter = new LicenseeListFilter();

        $licensor = $this->find();

        $this->listFilter->distributionChannels =
            $this->entityToOptionsService->entitiesToOptions($licensor->distributionChannels);
        $this->listFilter->territories =
            $this->entityToOptionsService->entitiesToOptions($licensor->territories);
        $this->listFilter->productCategories =
            $this->entityToOptionsService->entitiesToOptions($licensor->productCategories);

        return $this->listFilter;
    }

}
