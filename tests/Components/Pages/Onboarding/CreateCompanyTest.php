<?php

declare(strict_types=1);

namespace App\Tests\Components\Pages\Onboarding;

use App\DataFixtures\Tests\TestUserFixtures;
use App\Entity\Company;
use App\Tests\AppWebTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class CreateCompanyTest extends AppWebTestCase
{

    public function testCreateCompanyFrom()
    {
        $client = static::createClient();
        $component = $this->setupLiveComponent(
            $client,
            'pages:onboarding:create-company',
            [],
            TestUserFixtures::USERNAME_LICENSEE_MISSING_COMPANY
        );

        $component->render();
        self::assertSelectorExists('[name="company_form[name]"]');
        self::assertSelectorExists('[name="company_form[description]"]');
        self::assertSelectorExists('[name="company_form[website]"]');
        self::assertSelectorExists('[name="company_form[country]"]');
        self::assertSelectorExists('[name="company_form[logoFile]"]');

        $projectDir = $client->getKernel()->getProjectDir();

        $component->set('company_form.name', 'Test Case Company');
        $component->set('company_form.description', 'Test Case Description');
        $component->set('company_form.website', 'https://test-case.com');
        $component->set('company_form.country', 'NLD');

        $uploadedFile = new UploadedFile(
            $projectDir.'/tests/assets/test-upload-logo.jpg',
            'test-upload-logo.jpg'
        );

        /* @noinspection PhpParamsInspection */
        $component->call(
            action: 'save',
            files: ['company_form' => ['logoFile' => $uploadedFile]]
        );

        self::assertResponseRedirects('/onboarding/create_profile/step/1');

        // Check if company was created with correct values
        $entityManager = $client->getContainer()->get(EntityManagerInterface::class);
        /** @var Company|null $company */
        $company = $entityManager
            ->getRepository(Company::class)
            ->findOneBy(['name' => 'Test Case Company']);

        self::assertNotEmpty($company);
        self::assertEquals('Test Case Company', $company->name);
        self::assertEquals('Test Case Description', $company->description);
        self::assertEquals('https://test-case.com', $company->website);
        self::assertEquals('NLD', $company->country->isoCode);

        self::assertNotEmpty($company->logo);
        $fileSystem = $client->getContainer()->get('oneup_flysystem.user_uploads_filesystem');
        self::assertTrue($fileSystem->has($company->logo));
    }

}
