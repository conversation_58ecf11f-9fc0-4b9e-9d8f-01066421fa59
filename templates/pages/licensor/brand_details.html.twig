{% extends 'layout/app.html.twig' %}

{% block title %}{{ brand.name }}{% endblock %}

{% block meta %}
    <meta name="description" content="Explore our offerings your way"/>
    <meta name="keywords" content="licensor, brand, {{ brand.name }}"/>
{% endblock %}

{% block content %}
    <div class="space-y-12 mx-12" data-testid="brand-details-content">
        <div class="mt-12 mb-4">
            <twig:pages:licensor:dashboard-link/>
        </div>
        <div class="flex">
            <div class="flex-2/3">
                <div class="grid grid-cols-1 gap-4">
                    <div class="flex items-center">
                        <img
                            src="{{ brand.licensor.company.logo }}"
                            alt="{{ brand.licensor.company.name }}"
                            class="border-r border-black mr-10 pr-10"
                        />

                        <h1>{{ brand.name }}</h1>
                    </div>
                    <p class="mb-8 text-xl">{{ brand.description }}</p>
                    <div class="my-12 text-lg">
                        <div class="grid grid-cols-3 gap-4">
                            <div>
                                <p class="text-neutral font-semibold">Company</p>
                                {{ brand.licensor.company.name }}
                            </div>
                            <div>
                                <p class="text-neutral font-semibold">Location</p>
                                {{ brand.licensor.company.country.label }}
                            </div>
                            <div>
                                <p class="text-neutral font-semibold">Categories</p>
                                {{ brand.ipCategories|map((value) => value.name)|join(', ') }}
                            </div>
                        </div>
                    </div>
                    <div>
                        <span class="inline-block"><twig:pages:licensor:brand-pitch-button brand="{{ brand }}"/></span>
                        {% if brand.licensor.company.website %}
                            <span class="inline-block">
                                <twig:layout:forward-link
                                        url="{{ brand.licensor.company.website }}"
                                        text="Visit brand website"
                                        target="_blank"
                                        testid="brand-website-link"
                                />
                            </span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="flex-1/3">
                <img src="{{ brand.heroImage }}" alt="{{ brand.name }}"/>
            </div>
        </div>
        <div class="grid grid-cols-1 gap-8">
            <div>
                <h2>Preferred product categories</h2>
                <twig:layout:badge-list
                        class="my-4"
                        values="{{ brand.licensor.productCategories }}"
                        itemClass="big-badge"
                        labelClass="big-badge-label"
                        showAll
                />
            </div>
            <div>
                <h2>Territories</h2>
                <twig:layout:badge-list
                        class="my-4"
                        values="{{ brand.licensor.territories }}"
                        itemClass="big-badge"
                        labelClass="big-badge-label"
                        showAll
                />
            </div>
            <div>
                <h2>Distribution channels</h2>
                <twig:layout:badge-list
                        class="my-4"
                        values="{{ brand.licensor.distributionChannels }}"
                        itemClass="big-badge"
                        labelClass="big-badge-label"
                        showAll
                />
            </div>
            <div>
                <h2>Audience</h2>
                <div class="flex gap-8">
                    <twig:layout:badge-list
                            class="my-4 flex-1/2"
                            label="Age groups"
                            values="{{ brand.targetAudienceAgeGroups }}"
                            itemClass="big-badge"
                            labelClass="big-badge-label"
                            showAll
                    />
                    <twig:layout:badge-list
                            class="my-4 flex-1/2"
                            label="Genders"
                            values="{{ brand.targetAudienceGenders }}"
                            itemClass="big-badge"
                            labelClass="big-badge-label"
                            showAll
                    />
                </div>
            </div>
        </div>
        <!-- TODO(@edwinljacobs): Add brand galley images (more info needed)-->
        <!-- TODO(@edwinljacobs): Add brand documents (more info needed) -->
    </div>

    {% if not projects is empty %}
        <div class="mt-8 mb-4">
            <h2>{{ brand.name }} opportunities</h2>
        </div>

        {% for project in projects %}
            <div class="card mb-8 gap-8 flex flex-col md:flex-row w-full" data-testid="brand-project-list-item">
                <div class="w-full md:w-3/12 flex flex-col gap-2">
                    <span class="text-neutral">Product</span>
                    <a class="text-2xl font-semibold"
                       href="{{ url('app_licensor_project_details', {slug: project.slug}) }}">
                        {{ project.name }}
                    </a>

                    <p>{{ project.description }}</p>
                </div>

                <div class="w-full md:w-7/12">
                    <p class="text-neutral my-4">You align on the following criteria</p>
                    {% set licensee = licensee_finder.find %}
                    <div class="flex flex-wrap gap-y-4">
                        <twig:layout:match-item-alignment base="{{ licensee }}" against="{{ project }}"/>
                    </div>

                </div>

                <div class="md:w-2/12 flex flex-col w-md">
                    <div class="flex-grow">
                        <twig:pages:licensor:project-favorite-button projectId="{{ project.id }}"
                                                                     class="float-right"/>
                    </div>

                    <div class="space-y-1">
                        <twig:pages:licensor:project-bid-button project="{{ project }}"/>
                        {% block buttons %}{% endblock %}
                    </div>
                </div>
            </div>
        {% endfor %}

    {% endif %}
{% endblock %}
