{% set licensees = this.getLicenseeList() %}
<div {{ attributes }}>
    {{ form_start(form) }}
    <div class="float-right my-4">
        {{ form_row(form.search) }}
    </div>
    <div class="flex w-full gap-4">
        <div class="w-1/5 flex-none">
            {{ form_rest(form) }}
        </div>
        <div class="w-4/5 flex-none">
            <div class="grid grid-cols-1 gap-4 w-full">
                {% for licensee in licensees %}
                    <div class="flex card w-full">
                        <div class="w-3/10">
                            <twig:ux:icon name="mdi:image-outline"/>
                        </div>
                        <div class="w-5/10">
                            <div class="grid grid-cols-1 gap-4 w-full">
                                <div>
                                    <span class="inline-block">
                                        <img class="inline"
                                             src="{{ licensee.company.logo }}"
                                             width="75x"
                                             height="51px"
                                             title="{{ licensee.company.name }}"
                                             alt="{{licensee.company.name }}"
                                        />
                                    </span>
                                    <span class="inline-block">
                                        <twig:layout:forward-link
                                            url="{{ licensee.company.website }}"
                                            text="{{'Visit ' ~ licensee.company.name }}"
                                            class="text-primary-text font-semibold"
                                        />
                                    </span>
                                </div>
                                <div class="flex">
                                    <div class="w-3/10">
                                        <p>{{ licensee.company.description }}</p>
                                    </div>
                                    <div class="w-7/10 grid grid-cols-2 gap-2">
                                        <twig:layout:badge-list
                                                values="{{ licensee.territories }}"
                                                label="Territories"
                                                hideAfter="3"
                                        />
                                        <twig:layout:badge-list
                                                values="{{ licensee.productCategories }}"
                                                label="Product Categories"
                                                hideAfter="3"
                                        />
                                        <twig:layout:badge-list
                                                values="{{ licensee.distributionChannels }}"
                                                label="Distribution Channels"
                                                hideAfter="3"
                                        />
                                        <twig:layout:badge-list
                                                values="{{ licensee.targetAudienceAgeGroups }}"
                                                label="Age Groups"
                                                hideAfter="3"
                                        />
                                    </div>
                                </div>
                                <a href="{{ url('app_licensee_details', {'slug': licensee.company.slug}) }}" class="m-auto btn-secondary w-4/5 text-center">Explore Company Profile</a>
                            </div>
                        </div>
                        <div class="w-2/10">
                            <twig:pages:licensee:favorite-button licenseeId="{{ licensee.id }}" class="text-right"/>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
