<?php

declare(strict_types=1);

namespace App\Controller\Licensee;

use App\Entity\Licensee;
use App\Security\Voter\UserEmailVerifiedVoter;
use App\Security\Voter\UserHasLicensorProfileVoter;
use Symfony\Bridge\Doctrine\Attribute\MapEntity;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Authorization\Voter\AuthenticatedVoter;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted(AuthenticatedVoter::IS_AUTHENTICATED)]
#[IsGranted(UserEmailVerifiedVoter::class)]
#[IsGranted(UserHasLicensorProfileVoter::class)]
class LicenseeDetailsController extends AbstractController
{

    #[Route(path: '//licensor/manufacturer/{slug}', name: 'app_licensee_details')]
    public function index(
        #[MapEntity(expr: 'repository.getByCompanySlug(slug)')]
        Licensee $licensee
    ) {
        return $this->render('pages/licensee/licensee_details.html.twig', ['licensee' => $licensee]);
    }

}
