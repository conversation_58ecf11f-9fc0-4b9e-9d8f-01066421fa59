<?php

namespace App\Controller;

use App\Entity\User;
use App\Entity\UserScope;
use App\Security\Voter\UserEmailVerifiedVoter;
use App\Security\Voter\UserHasProfileVoter;
use App\Service\LicenseeService;
use App\Service\LicensorService;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Authorization\Voter\AuthenticatedVoter;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted(AuthenticatedVoter::IS_AUTHENTICATED)]
#[IsGranted(UserEmailVerifiedVoter::class)]
#[IsGranted(UserHasProfileVoter::class)]
final class IndexController extends AbstractController
{

    public function __construct(
        private readonly LicenseeService $licenseeService,
        private readonly LicensorService $licensorService,
    ) {
    }

    #[Route('/', name: self::class)]
    public function index(LoggerInterface $log): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        switch ($user->defaultScope) {
            case UserScope::SCOPE_INVESTOR:
            case UserScope::SCOPE_LICENSOR:
                return $this->redirectToRoute(
                    'app_licensee_list',
                    ['filter' => $this->licensorService->getListFilter()]
                );
            case UserScope::SCOPE_LICENSEE:
                return $this->redirectToRoute(
                    'app_licensor_project_list',
                    ['filter' => $this->licenseeService->getProjectListFilter()]
                );
            default:
                throw new \RuntimeException('Default scope must be set');
        }
    }

}
