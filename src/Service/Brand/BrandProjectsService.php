<?php

declare(strict_types=1);

namespace App\Service\Brand;

use App\Entity\Brand;
use App\Entity\Project;
use Doctrine\Common\Collections\Collection;

class BrandProjectsService
{

    /**
     * @return Collection<int, Project>
     */
    public function getBrandOpenProjects(Brand $brand): Collection
    {
        return $brand->projects->filter(static fn (Project $project) => $project->public && $project->bidEnabled);
    }

}
