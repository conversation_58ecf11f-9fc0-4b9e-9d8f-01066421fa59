<?php

namespace App\Entity;

use App\Repository\LicensorRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: LicensorRepository::class)]
#[ORM\UniqueConstraint(columns: ['company_id'])]
class Licensor
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    public ?int $id = null;

    #[ORM\OneToOne(targetEntity: Company::class, fetch: 'EAGER')]
    public Company $company;

    /**
     * @var Collection<int, ProductCategory>
     */
    #[ORM\ManyToMany(targetEntity: ProductCategory::class)]
    public Collection $productCategories;

    /**
     * @var Collection<int, DistributionChannel>
     */
    #[ORM\ManyToMany(targetEntity: DistributionChannel::class)]
    public Collection $distributionChannels;

    /**
     * @var Collection<int, Territory>
     */
    #[ORM\ManyToMany(targetEntity: Territory::class)]
    public Collection $territories;

    /**
     * @var Collection<int, Brand>
     */
    #[ORM\OneToMany(targetEntity: Brand::class, mappedBy: 'licensor')]
    public Collection $brands;

    /**
     * @var Collection<int, Licensee>
     */
    #[ORM\ManyToMany(targetEntity: Licensee::class, cascade: ['remove'])]
    #[ORM\JoinTable(name: 'licensor_favorite_licensees')]
    public Collection $favoriteLicensees;

    public function __construct()
    {
        $this->productCategories = new ArrayCollection();
        $this->distributionChannels = new ArrayCollection();
        $this->territories = new ArrayCollection();
        $this->brands = new ArrayCollection();
        $this->favoriteLicensees = new ArrayCollection();
    }

}
