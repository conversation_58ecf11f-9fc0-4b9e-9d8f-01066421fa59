<?php

declare(strict_types=1);

namespace App\Twig\Components\Pages\Onboarding;

use App\Entity\Licensor;
use App\Entity\User;
use App\Security\Voter\UserEmailVerifiedVoter;
use App\Security\Voter\UserHasCompanyVoter;
use App\Security\Voter\UserHasScopeVoter;
use Symfony\Component\Security\Core\Authorization\Voter\AuthenticatedVoter;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\Attribute\LiveProp;
use Symfony\UX\LiveComponent\ComponentWithFormTrait;
use Symfony\UX\LiveComponent\DefaultActionTrait;

#[IsGranted(AuthenticatedVoter::IS_AUTHENTICATED)]
#[IsGranted(UserHasScopeVoter::class)]
#[IsGranted(UserEmailVerifiedVoter::class)]
#[IsGranted(UserHasCompanyVoter::class)]
#[AsLiveComponent('pages:onboarding:create-licensor', template: 'components/pages/onboarding/create-licensor.html.twig')]
class CreateLicensor extends CreateProfile
{
    use DefaultActionTrait;
    use ComponentWithFormTrait;

    #[LiveProp]
    public ?Licensor $profile = null;

    protected function getProfile(): Licensor
    {
        if ($this->profile === null) {
            /** @var User $user */
            $user = $this->getUser();
            $this->profile = $this->profileService->findLicensorByUser($user) ?: new Licensor();
        }

        return $this->profile;
    }

}
