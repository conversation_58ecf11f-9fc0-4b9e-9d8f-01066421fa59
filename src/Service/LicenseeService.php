<?php

declare(strict_types=1);

namespace App\Service;

use App\DTO\Licensor\ProjectListFilter;
use App\Entity\Licensee;
use App\Entity\UserScope;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class LicenseeService
{

    private ?ProjectListFilter $projectListFilter = null;

    public function __construct(
        private readonly TokenStorageInterface  $tokenStorage,
        private readonly ProfileService         $profileService,
        private readonly EntityToOptionsService $entityToOptionsService,
    ) {
    }

    /**
     * Resolves the licensee of the currently authenticated user. If it is not there it will break.
     * This is intended to be used in places where the licensee is required.
     * Note that this is exposed to Twig as a global service.
     *
     * @see config/packages/twig.yaml
     */
    public function find(): Licensee
    {
        /** @var \App\Entity\User|null $user */
        $user = $this->tokenStorage->getToken()?->getUser();
        if (!$user || UserScope::SCOPE_LICENSEE !== $user->defaultScope) {
            throw new \RuntimeException('User not authenticated');
        }

        $licensee = $this->profileService->findLicenseeByUser($user);
        if (!$licensee) {
            throw new \RuntimeException('Licensee not found');
        }

        return $licensee;
    }

    public function getProjectListFilter(): ProjectListFilter
    {
        if (!$this->projectListFilter) {
            $this->projectListFilter = new ProjectListFilter();

            $licensee = $this->find();
            $this->projectListFilter->targetAudienceAgeGroups =
                $this->entityToOptionsService->entitiesToOptions($licensee->targetAudienceAgeGroups) ?: null;
            $this->projectListFilter->targetAudienceGenders =
                $this->entityToOptionsService->entitiesToOptions($licensee->targetAudienceGenders) ?: null;
            $this->projectListFilter->territories =
                $this->entityToOptionsService->entitiesToOptions($licensee->territories) ?: null;
            $this->projectListFilter->productCategories =
                $this->entityToOptionsService->entitiesToOptions($licensee->productCategories) ?: null;
            $this->projectListFilter->ipCategories =
                $this->entityToOptionsService->entitiesToOptions($licensee->ipCategoryInterests) ?: null;
            $this->projectListFilter->distributionChannels =
                $this->entityToOptionsService->entitiesToOptions($licensee->distributionChannels) ?: null;
        }

        return $this->projectListFilter;
    }

}
