<?php

declare(strict_types=1);

namespace App\Twig\Components\Pages\Licensor;

use App\Entity\Brand;
use App\Service\Brand\BrandProjectsService;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\Attribute\LiveProp;
use Symfony\UX\LiveComponent\DefaultActionTrait;

#[AsLiveComponent(name: 'pages:licensor:brand-opportunity-list')]
class BrandOpportunityList
{
    use DefaultActionTrait;

    #[LiveProp]
    public ?int $brandId = null;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly BrandProjectsService   $brandProjectsService
    ) {
    }

    /**
     * @return Collection<int, \App\Entity\Project>
     *
     * @throws \Doctrine\ORM\Exception\ORMException
     */
    public function getBrandOpportunities(): Collection
    {
        $brand = $this->entityManager->find(Brand::class, $this->brandId);

        return $brand === null
            ? new ArrayCollection()
            : $this->brandProjectsService->getBrandOpenProjects($brand);
    }

}
