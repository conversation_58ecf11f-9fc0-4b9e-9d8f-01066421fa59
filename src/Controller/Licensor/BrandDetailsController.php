<?php

declare(strict_types=1);

namespace App\Controller\Licensor;

use App\Entity\Brand;
use App\Security\Voter\UserEmailVerifiedVoter;
use App\Security\Voter\UserHasLicenseeProfileVoter;
use App\Service\Brand\BrandProjectsService;
use Symfony\Bridge\Doctrine\Attribute\MapEntity;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Authorization\Voter\AuthenticatedVoter;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted(AuthenticatedVoter::IS_AUTHENTICATED)]
#[IsGranted(UserEmailVerifiedVoter::class)]
#[IsGranted(UserHasLicenseeProfileVoter::class)]
class BrandDetailsController extends AbstractController
{

    #[Route(path: '/licensor/brand/{slug}', name: 'app_licensor_brand_details')]
    public function index(
        #[MapEntity(mapping: ['slug' => 'slug'])]
        Brand                $brand,
        BrandProjectsService $brandProjectsService,
    ): Response {
        return $this->render(
            'pages/licensor/brand_details.html.twig',
            [
                'brand' => $brand,
                'projects' => $brandProjectsService->getBrandOpenProjects($brand),
            ]
        );
    }

}
