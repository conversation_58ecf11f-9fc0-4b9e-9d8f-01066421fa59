<?php

namespace App\Entity;

use App\Interfaces\MatchItemInterface;
use App\Interfaces\SelectableInterface;
use App\Repository\BrandRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: BrandRepository::class)]
#[ORM\UniqueConstraint(columns: ['slug'])]
class Brand implements SelectableInterface, MatchItemInterface
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    public ?int $id = null;

    #[ORM\Column(length: 255)]
    public ?string $slug = null;

    #[ORM\Column(length: 255)]
    public ?string $name = null;

    #[ORM\Column(type: 'text')]
    public ?string $description = null;

    #[ORM\Column(length: 255, nullable: true)]
    public ?string $logo = null;

    #[ORM\Column(length: 255, nullable: true)]
    public ?string $heroImage = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    public ?int $preferredMinimumGuaranteeMin = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    public ?int $preferredMinimumGuaranteeMax = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 8, scale: 5, nullable: true)]
    public ?string $preferredRoyaltyRateMin = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 8, scale: 5, nullable: true)]
    public ?string $preferredRoyaltyRateMax = null;

    #[ORM\ManyToOne(targetEntity: Licensor::class, inversedBy: 'brands')]
    #[ORM\JoinColumn(nullable: false)]
    public ?Licensor $licensor = null;

    /**
     * @var Collection<int, Project>
     */
    #[ORM\OneToMany(targetEntity: Project::class, mappedBy: 'brand')]
    public Collection $projects;

    /**
     * @var Collection<int, AgeGroup>
     */
    #[ORM\ManyToMany(targetEntity: AgeGroup::class)]
    public Collection $targetAudienceAgeGroups;

    /**
     * @var Collection<int, Gender>
     */
    #[ORM\ManyToMany(targetEntity: Gender::class)]
    public Collection $targetAudienceGenders;

    /**
     * @var Collection<int, IPCategory>
     */
    #[ORM\ManyToMany(targetEntity: IPCategory::class)]
    #[ORM\JoinTable(name: 'brand_ip_categories')]
    public Collection $ipCategories;

    public function __construct()
    {
        $this->targetAudienceGenders = new ArrayCollection();
        $this->targetAudienceAgeGroups = new ArrayCollection();
        $this->ipCategories = new ArrayCollection();
        $this->projects = new ArrayCollection();
    }

    public function getLabel(): string
    {
        return $this->name;
    }

    public function getValue(): string
    {
        return $this->slug;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getHeroImage(): ?string
    {
        return $this->heroImage;
    }

    /**
     * @var Collection<int, Territory>
     */
    public Collection $territories {
        get => $this->licensor?->territories ?? new ArrayCollection();
    }

    /**
     * @var Collection<int, ProductCategory>
     */
    public Collection $productCategories {
        get => $this->licensor?->productCategories ?? new ArrayCollection();
    }

    /**
     * @var Collection<int, DistributionChannel>
     */
    public Collection $distributionChannels {
        get => $this->licensor?->distributionChannels ?? new ArrayCollection();
    }

}
