<?php

declare(strict_types=1);

namespace App\Twig\Components\Layout;

use App\Entity\Investor;
use App\Entity\Licensee;
use App\Entity\Licensor;
use App\Entity\User;
use App\Service\ProfileService;
use App\Service\UserSettings;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\Attribute\LiveAction;
use Symfony\UX\LiveComponent\DefaultActionTrait;

#[AsLiveComponent(name: 'layout:profile-completion', template: 'components/layout/profile-completion.html.twig')]
class ProfileCompletion extends AbstractController
{
    use DefaultActionTrait;

    public function __construct(
        private readonly TokenStorageInterface $tokenStorage,
        private readonly ProfileService        $profileService,
        private readonly UserSettings          $userSettings
    ) {
    }

    #[LiveAction]
    public function hide(): void
    {
        $this->userSettings->dismissProfileCompletion();
    }

    public function isVisible(): bool
    {
        return null !== $this->getProfile() && !$this->userSettings->hasDismissedProfileCompletion();
    }

    public function getPercentage(): ?int
    {
        $profile = $this->getProfile();

        return match (true) {
            $profile instanceof Licensor => $this->getLicensorPercentage($profile),
            $profile instanceof Licensee => $this->getLicenseePercentage($profile),
            $profile instanceof Investor => $this->getInvestorPercentage($profile),
            default => null,
        };
    }

    private function getProfile(): mixed
    {
        $user = $this->tokenStorage->getToken()?->getUser();
        if (!$user) {
            return null;
        }

        /* @var User $user */
        return $this->profileService->findByUser($user);
    }

    private function getLicensorPercentage(Licensor $profile): int
    {
        $pointsToFill = 40;
        if ($profile->company->logo) {
            $pointsToFill -= 10;
        }

        if (!count($profile->brands)) {
            return 100 - $pointsToFill;
        }

        // For each brand there are 2 optional image fields
        $imageFieldCount = count($profile->brands) * 2;
        $brandImagePointValue = $pointsToFill / $imageFieldCount;
        foreach ($profile->brands as $brand) {
            if ($brand->logo) {
                $pointsToFill -= $brandImagePointValue;
            }

            if ($brand->heroImage) {
                $pointsToFill -= $brandImagePointValue;
            }
        }

        return (int) (100 - $pointsToFill);
    }

    private function getLicenseePercentage(Licensee $profile): int
    {
        // Currently there is only an optional logo field
        return $profile->company->logo ? 100 : 80;
    }

    private function getInvestorPercentage(Investor $profile): int
    {
        return 80;
    }

}
