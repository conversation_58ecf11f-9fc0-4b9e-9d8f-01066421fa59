<?php

declare(strict_types=1);

namespace App\Tests\Components\Pages\Licensee;

use App\Entity\Licensee;
use App\Entity\Licensor;
use App\Tests\AppWebTestCase;
use Doctrine\ORM\EntityManagerInterface;

class LicenseeFavoriteButtonTest extends AppWebTestCase
{

    public function testLicensorFavoriteButtonComponent(): void
    {
        $client = self::createClient();
        $entityManager = $client->getContainer()->get(EntityManagerInterface::class);

        /** @var \App\Repository\LicenseeRepository $licenseeRepository */
        $licenseeRepository = $entityManager->getRepository(Licensee::class);
        $licensee = $licenseeRepository->getByCompanySlug('hasbro');
        self::assertNotEmpty($licensee);

        $component = $this->setupLiveComponent(
            client: $client,
            componentName: 'pages:licensee:favorite-button',
            componentData: ['licenseeId' => $licensee->id],
            userName: '<EMAIL>',
        );

        // Check if the licensee is now a favorite for the licensor
        $component->call('toggle');

        /** @var \App\Repository\LicensorRepository $licensorRepository */
        $licensorRepository = $entityManager->getRepository(Licensor::class);
        $licensor = $licensorRepository->getByCompanySlug('test-licensor-company');
        self::assertNotEmpty($licensor);

        self::assertContains(
            $licensee->id,
            $licensor->favoriteLicensees->map(fn ($licensor) => $licensor->id)
        );
    }

}
