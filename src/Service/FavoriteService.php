<?php

declare(strict_types=1);

namespace App\Service;

use App\Entity\Brand;
use App\Entity\Licensee;
use App\Entity\Licensor;
use App\Entity\Project;
use Doctrine\ORM\EntityManagerInterface;

readonly class FavoriteService
{

    public function __construct(private EntityManagerInterface $entityManager)
    {
    }

    public function toggleLicenseeFavoriteProject(Licensee $licensee, Project $project): void
    {
        $this->isProjectFavoriteForLicensee($licensee, $project)
            ? $this->removeLicenseeFavoriteProject($licensee, $project)
            : $this->addLicenseeFavoriteProject($licensee, $project);
    }

    public function addLicenseeFavoriteProject(Licensee $licensee, Project $project): void
    {
        if (!$this->isProjectFavoriteForLicensee($licensee, $project)) {
            $licensee->favoriteProjects->add($project);
            $this->entityManager->flush();
        }
    }

    public function removeLicenseeFavoriteProject(Licensee $licensee, Project $project): void
    {
        if ($licensee->favoriteProjects->removeElement($project)) {
            $this->entityManager->flush();
        }
    }

    public function isProjectFavoriteForLicensee(Licensee $licensee, Project $project): bool
    {
        return $licensee->favoriteProjects->contains($project);
    }

    public function toggleLicenseeFavoriteBrand(Licensee $licensee, Brand $brand): void
    {
        $this->isBrandFavoriteForLicensee($licensee, $brand)
            ? $this->removeLicenseeFavoriteBrand($licensee, $brand)
            : $this->addLicenseeFavoriteBrand($licensee, $brand);
    }

    public function addLicenseeFavoriteBrand(Licensee $licensee, Brand $brand): void
    {
        if (!$this->isBrandFavoriteForLicensee($licensee, $brand)) {
            $licensee->favoriteBrands->add($brand);
            $this->entityManager->flush();
        }
    }

    public function removeLicenseeFavoriteBrand(Licensee $licensee, Brand $brand): void
    {
        if ($licensee->favoriteBrands->removeElement($brand)) {
            $this->entityManager->flush();
        }
    }

    public function isBrandFavoriteForLicensee(Licensee $licensee, Brand $brand): bool
    {
        return $licensee->favoriteBrands->contains($brand);
    }

    public function toggleLicensorFavoriteLicensee(Licensor $licensor, Licensee $licensee): void
    {
        $this->isLicenseeFavoriteForLicensor($licensor, $licensee)
            ? $this->removeLicensorFavoriteLicensee($licensor, $licensee)
            : $this->addLicensorFavoriteLicensee($licensor, $licensee);
    }

    public function addLicensorFavoriteLicensee(Licensor $licensor, Licensee $licensee): void
    {
        if (!$this->isLicenseeFavoriteForLicensor($licensor, $licensee)) {
            $licensor->favoriteLicensees->add($licensee);
            $this->entityManager->flush();
        }
    }

    public function removeLicensorFavoriteLicensee(Licensor $licensor, Licensee $licensee): void
    {
        if ($licensor->favoriteLicensees->removeElement($licensee)) {
            $this->entityManager->flush();
        }
    }

    public function isLicenseeFavoriteForLicensor(Licensor $licensor, Licensee $licensee): bool
    {
        return $licensor->favoriteLicensees->contains($licensee);
    }

}
