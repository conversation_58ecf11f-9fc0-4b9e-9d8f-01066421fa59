<?php

declare(strict_types=1);

namespace App\Twig\Components\Pages\Licensee;

use App\DTO\Licensee\LicenseeListFilter;
use App\Form\Licensee\LicenseeFilterFormType;
use App\Model\PaginationContext;
use App\Repository\LicenseeRepository;
use Knp\Component\Pager\Pagination\PaginationInterface;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Routing\RequestContext;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\Attribute\LiveProp;
use Symfony\UX\LiveComponent\ComponentWithFormTrait;
use Symfony\UX\LiveComponent\DefaultActionTrait;

#[AsLiveComponent(name: 'pages:licensee:licensee-list')]
class LicenseeList extends AbstractController
{
    use DefaultActionTrait;
    use ComponentWithFormTrait;

    #[LiveProp(writable: true, url: true)]
    public LicenseeListFilter $filter;

    #[LiveProp(writable: true, url: true)]
    public int $page = 1;

    public function __construct(
        private readonly PaginatorInterface $paginator,
        private readonly RequestContext     $requestContext,
        private readonly PaginationContext  $paginationContext,
        private readonly LicenseeRepository $licenseeRepository,
    ) {
        $this->filter = new LicenseeListFilter();
        $this->page = 1;
    }

    protected function instantiateForm(): FormInterface
    {
        return $this->createForm(LicenseeFilterFormType::class, $this->filter);
    }

    /**
     * @return PaginationInterface<int, \App\Entity\Licensee>
     */
    public function getLicenseeList(): PaginationInterface
    {
        // _live_component Workaround is to prevent rendering error on ajax calls
        $this->requestContext->setParameter('_live_component', 'pages:licensee:licensee-list');

        return $this->paginator->paginate(
            $this->licenseeRepository->findByLicenseeFilter($this->filter),
            $this->page,
            $this->paginationContext->getDefaultPageSize(),
        );
    }

}
