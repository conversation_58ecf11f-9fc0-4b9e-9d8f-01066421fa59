<?php

namespace App\Repository;

use App\DTO\Licensee\LicenseeListFilter;
use App\Entity\Licensee;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Licensee>
 */
class LicenseeRepository extends ServiceEntityRepository
{

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Licensee::class);
    }

    public function getByCompanySlug(string $companySlug): ?Licensee
    {
        return $this->createQueryBuilder('licensee')
            ->innerJoin('licensee.company', 'company')
            ->andWhere('company.slug = :slug')
            ->setParameter('slug', $companySlug)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @return Licensee[]
     */
    public function findByLicenseeFilter(LicenseeListFilter $filter): array
    {
        $qb = $this->createQueryBuilder('licensee');
        $qb->innerJoin('licensee.company', 'company');
        $qb->innerJoin('licensee.productCategories', 'productCategories');
        $qb->innerJoin('licensee.territories', 'territories');
        $qb->innerJoin('licensee.distributionChannels', 'distributionChannels');
        $qb->innerJoin('licensee.targetAudienceAgeGroups', 'targetAudienceAgeGroups');
        $qb->innerJoin('licensee.targetAudienceGenders', 'targetAudienceGenders');

        $qb->select(['licensee', 'company']);

        if ($filter->search) {
            $qb->andWhere($qb->expr()->like('company.name', ':search'))
                ->setParameter('search', '%'.$filter->search.'%');
        }

        if ($filter->productCategories) {
            $qb->andWhere($qb->expr()->in('productCategories.slug', ':productCategories'))
                ->setParameter('productCategories', $filter->productCategories);
        }

        if ($filter->territories) {
            $qb->andWhere($qb->expr()->in('territories.slug', ':territories'))
                ->setParameter('territories', $filter->territories);
        }

        if ($filter->distributionChannels) {
            $qb->andWhere($qb->expr()->in('distributionChannels.slug', ':distributionChannels'))
                ->setParameter('distributionChannels', $filter->distributionChannels);
        }

        if ($filter->targetAudienceAgeGroups) {
            $qb->andWhere($qb->expr()->in('targetAudienceAgeGroups.slug', ':targetAudienceAgeGroups'))
                ->setParameter('targetAudienceAgeGroups', $filter->targetAudienceAgeGroups);
        }

        if ($filter->targetAudienceGenders) {
            $qb->andWhere($qb->expr()->in('targetAudienceGenders.slug', ':targetAudienceGenders'))
                ->setParameter('targetAudienceGenders', $filter->targetAudienceGenders);
        }

        return $qb->getQuery()->getResult();
    }

}
