<?php

declare(strict_types=1);

namespace App\Service;

use App\Exception\FileUploadException;
use League\Flysystem\Filesystem;
use League\Flysystem\FilesystemException;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\HttpFoundation\File\UploadedFile;

readonly class UploadService
{

    public function __construct(
        #[Autowire(service: 'oneup_flysystem.user_uploads_filesystem')]
        private Filesystem $userUploadsFileSystem,
    ) {
    }

    /**
     * @return string The location of the new file
     *
     * @throws FileUploadException
     */
    public function uploadFile(
        UploadedFile $file,
        string       $folder,
        string       $fileName,
        bool         $replace = false
    ): string {
        try {
            $fileLocation = sprintf(
                '%s/%s',
                $folder,
                $this->getFileName($file, $fileName)
            );

            $stream = fopen($file->getPathname(), 'rb');
            $this->userUploadsFileSystem->writeStream($fileLocation, $stream);
            if ($replace) {
                // Remove all files in the folder that start with the same name
                foreach ($this->userUploadsFileSystem->listContents($folder) as $fileAttributes) {
                    if ($fileAttributes['type'] !== 'file') {
                        continue;
                    }

                    $path = $fileAttributes['path'];
                    if ($path === $fileLocation) {
                        continue;
                    }

                    if (!str_starts_with(pathinfo($path, PATHINFO_FILENAME), $fileName)) {
                        continue;
                    }

                    $this->userUploadsFileSystem->delete($path);
                }
            }

            return $fileLocation;
        } catch (FilesystemException $e) {
            // Wrap
            throw FileUploadException::createFromFilesystemException($e);
        } finally {
            if (isset($stream)) {
                fclose($stream);
            }
        }
    }

    /**
     * We just use the field name.
     */
    private function getFileName(UploadedFile $file, string $fileName): string
    {
        /* @noinspection NonSecureUniqidUsageInspection */
        return sprintf(
            '%s-%s.%s',
            $fileName,
            uniqid(),
            $file->getClientOriginalExtension()
        );
    }

}
