<?php

use Erick<PERSON>krauch\PhpCsFixer\Fixers;

$finder = new PhpCsFixer\Finder()
    ->in(__DIR__)
    ->exclude('var')
;

return new PhpCsFixer\Config()
    ->setParallelConfig(PhpCsFixer\Runner\Parallel\ParallelConfigFactory::detect())
    ->registerCustomFixers(new Fixers())
    ->setRules([
        '@PSR12' => true,
        '@Symfony' => true,
        'yoda_style' => false,
        '@DoctrineAnnotation' => true,
        '@PHP84Migration' => true,
        'ErickSkrauch/align_multiline_parameters' => true,
        'ErickSkrauch/blank_line_around_class_body' => true,
        'ErickSkrauch/blank_line_before_return' => true,
        'ErickSkrauch/line_break_after_statements' => true,
        'ErickSkrauch/multiline_if_statement_braces' => true,
        'ErickSkrauch/ordered_overrides' => true,
    ])
    ->setFinder($finder)
;
