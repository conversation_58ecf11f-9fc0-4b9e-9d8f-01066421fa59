on:
  push:
    branches:
      - main

jobs:
  test:
    runs-on: tedatech
    timeout-minutes: 10
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      - run: devbox run test

  release:
    runs-on: tedatech
    timeout-minutes: 10
    needs:
      - test
    permissions:
      contents: write
      issues: write
      packages: write
      pull-requests: write
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      - id: semantic-release-tags
        uses: cycjimmy/semantic-release-action@v4
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          dry_run: true
          semantic_version: "v24.2.3"
      - uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3
        if: ${{ steps.semantic-release-tags.outputs.new_release_version != '' }}
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - uses: hiberbee/github-action-skaffold@e3f5dd5659610cb792c6cf32daaa797ea6d89842 # 1.27.0
        if: ${{ steps.semantic-release-tags.outputs.new_release_version != '' }}
        with:
          command: build
          push: true
          tag: ${{ steps.semantic-release-tags.outputs.new_release_version }}
          skaffold-version: 2.14.2
      - uses: cycjimmy/semantic-release-action@v4
        if: ${{ steps.semantic-release-tags.outputs.new_release_version != '' }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
