<?php

declare(strict_types=1);

namespace App\Tests\Components\Pages\Licensor;

use App\Entity\Licensee;
use App\Entity\Licensor;
use App\Entity\Project;
use App\Tests\AppWebTestCase;
use Doctrine\ORM\EntityManagerInterface;

class ProjectFavoriteButtonTest extends AppWebTestCase
{

    public function testLicensorFavoriteButtonComponent(): void
    {
        $client = self::createClient();
        $entityManager = $client->getContainer()->get(EntityManagerInterface::class);

        /** @var \App\Repository\ProjectRepository $projectRepository */
        $projectRepository = $entityManager->getRepository(Project::class);
        $project = $projectRepository->findOneBy(['slug' => 'disney-princess-royal-collection']);
        self::assertNotEmpty($project);

        $component = $this->setupLiveComponent(
            client: $client,
            componentName: 'pages:licensor:project-favorite-button',
            componentData: ['projectId' => $project->id],
            userName: '<EMAIL>',
        );

        // Check if the licensee is now a favorite for the licensor
        $component->call('toggle');

        /** @var \App\Repository\LicenseeRepository $licenseeRepository */
        $licenseeRepository = $entityManager->getRepository(Licensee::class);
        $licensee = $licenseeRepository->getByCompanySlug('test-licensee-company');
        self::assertNotEmpty($licensee);
        self::assertContains(
            $project->id,
            $licensee->favoriteProjects->map(fn ($project) => $project->id)
        );
    }

}
