<?php

declare(strict_types=1);

namespace App\DataFixtures\Tests;

use App\DataFixtures\AppFixture;
use App\DataFixtures\DistributionChannelFixtures;
use App\DataFixtures\ProductCategoryFixtures;
use App\DataFixtures\TerritoryFixtures;
use App\Entity\Company;
use App\Entity\DistributionChannel;
use App\Entity\Licensor;
use App\Entity\ProductCategory;
use App\Entity\Territory;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class LicensorFixtures extends AppFixture implements FixtureGroupInterface, DependentFixtureInterface
{

    private const array LICENSORS = [
        [
            'name' => 'disney',
            'product_category_interests' => ['toys', 'seasonal'],
            'territories' => [
                'united-states',
                'canada',
                'brazil',
                'united-kingdom',
                'france',
                'germany',
                'japan',
                'china',
                'australia',
            ],
            'distribution_channels' => [
                'hyper-supermarket',
                'department-store',
                'online-retailer',
                'mobile-apps-platforms',
                'seasonal-store',
                'gaming-metaverse',
            ],
        ],
        [
            'name' => 'warner-bros',
            'product_category_interests' => ['footwear', 'apparel'],
            'territories' => [
                'mexico',
                'argentina',
                'spain',
                'italy',
                'south-africa',
                'india',
                'indonesia',
            ],
            'distribution_channels' => [
                'speciality-store',
                'department-store',
                'e-commerce-of-speciality-store',
                'online-retailer',
                'chain-store',
                'mobile-apps-platforms',
            ],
        ],
        [
            'name' => 'nintendo',
            'product_category_interests' => ['footwear', 'electronics', 'apparel'],
            'territories' => [
                'united-states',
                'canada',
                'japan',
                'south-korea',
                'germany',
                'france',
                'united-kingdom',
                'australia',
            ],
            'distribution_channels' => [
                'online-retailer',
                'console',
                'gaming-metaverse',
                'speciality-store',
                'e-commerce-of-speciality-store',
                'mobile-apps-platforms',
                'department-store',
            ],
        ],
        [
            'name' => 'hasbro-entertainment',
            'product_category_interests' => ['apparel', 'footwear', 'seasonal'],
            'territories' => [
                'brazil',
                'colombia',
                'peru',
                'poland',
                'czechia',
                'egypt',
                'thailand',
                'vietnam',
            ],
            'distribution_channels' => [
                'discounter',
                'hyper-supermarket',
                'seasonal-store',
                'online-retailer',
                'mobile-apps-platforms',
                'chain-store',
            ],
        ],
        [
            'name' => 'hasbro-games',
            'product_category_interests' => ['toys', 'home', 'seasonal'],
            'territories' => [
                'united-states',
                'canada',
                'mexico',
                'united-kingdom',
                'germany',
                'france',
                'china',
                'india',
                'australia',
            ],
            'distribution_channels' => [
                'speciality-store',
                'discounter',
                'online-retailer',
                'department-store',
                'grocery-store',
                'seasonal-store',
            ],
        ],
        [
            'name' => 'sanrio',
            'product_category_interests' => ['electronics', 'seasonal', 'footwear'],
            'territories' => [
                'japan',
                'south-korea',
                'taiwan',
                'hong-kong',
                'singapore',
                'philippines',
                'united-states',
                'brazil',
            ],
            'distribution_channels' => [
                'speciality-store',
                'department-store',
                'online-retailer',
                'mobile-apps-platforms',
                'seasonal-store',
                'e-commerce-of-speciality-store',
                'drugstore',
            ],
        ],
        [
            'name' => 'mattel-creations',
            'product_category_interests' => ['toys', 'apparel', 'footwear'],
            'territories' => [
                'united-states',
                'mexico',
                'brazil',
                'argentina',
                'united-kingdom',
                'germany',
                'france',
                'australia',
                'india',
            ],
            'distribution_channels' => [
                'hyper-supermarket',
                'discounter',
                'online-retailer',
                'department-store',
                'speciality-store',
                'cash-carry',
            ],
        ],
        // TEST LICENSOR (ACTUAL TEST ACCOUNT)
        [
            'name' => 'test-licensor-company',
            'product_category_interests' => ['toys', 'apparel', 'electronics'],
            'territories' => [
                'puerto-rico',
                'costa-rica',
                'ecuador',
                'bolivia',
                'hungary',
                'romania',
                'uae',
                'qatar',
                'fiji',
                'samoa',
            ],
            'distribution_channels' => [
                'online-retailer',
                'mobile-apps-platforms',
                'web-3-blockchain-marketplaces',
                'digital-publishing-platforms',
                'xr-stores',
                'gaming-metaverse',
                'ai',
            ],
        ],
    ];

    public function load(ObjectManager $manager): void
    {
        foreach (self::LICENSORS as $licensorData) {
            $licensor = new Licensor();
            $licensor->company = $this->getFromRepository($licensorData['name'], Company::class);

            foreach ($licensorData['product_category_interests'] as $categoryName) {
                $category = $this->getFromRepository($categoryName, ProductCategory::class);
                $licensor->productCategories->add($category);
            }

            foreach ($licensorData['territories'] as $territoryName) {
                $territory = $this->getFromRepository($territoryName, Territory::class);
                $licensor->territories->add($territory);
            }

            foreach ($licensorData['distribution_channels'] as $channelName) {
                $channel = $this->getFromRepository($channelName, DistributionChannel::class);
                $licensor->distributionChannels->add($channel);
            }

            $manager->persist($licensor);
            $this->addToRepository($licensorData['name'], $licensor);
        }

        $manager->flush();
    }

    public function getDependencies(): array
    {
        return [
            CompanyFixtures::class,
            ProductCategoryFixtures::class,
            DistributionChannelFixtures::class,
            TerritoryFixtures::class,
        ];
    }

    public static function getGroups(): array
    {
        return ['tests'];
    }

}
