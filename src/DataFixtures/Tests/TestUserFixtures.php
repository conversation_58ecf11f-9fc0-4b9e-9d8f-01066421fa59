<?php

namespace App\DataFixtures\Tests;

use App\DataFixtures\AppFixture;
use App\DataFixtures\CountryFixtures;
use App\Entity\User;
use App\Entity\UserRole;
use App\Entity\UserScope;
use Doctrine\Bundle\FixturesBundle\FixtureGroupInterface;
use Doctrine\Common\DataFixtures\DependentFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;

class TestUserFixtures extends AppFixture implements FixtureGroupInterface, DependentFixtureInterface
{

    public const string USER_PASSWORD = 'password123';
    public const string USERNAME_UNVERIFIED = '<EMAIL>';
    public const string USERNAME_MISSING_SCOPE = '<EMAIL>';
    public const string USERNAME_LICENSEE = '<EMAIL>';
    public const string USERNAME_LICENSEE_MISSING_COMPANY = '<EMAIL>';
    public const string USERNAME_LICENSEE_MISSING_PROFILE = '<EMAIL>';
    public const string USERNAME_LICENSOR = '<EMAIL>';
    public const string USERNAME_LICENSOR_MISSING_COMPANY = '<EMAIL>';
    public const string USERNAME_LICENSOR_MISSING_PROFILE = '<EMAIL>';
    public const string USERNAME_INVESTOR = '<EMAIL>';
    public const string USERNAME_INVESTOR_MISSING_COMPANY = '<EMAIL>';
    public const string USERNAME_INVESTOR_MISSING_PROFILE = '<EMAIL>';
    public const string USERNAME_ADMIN = '<EMAIL>';
    public const string USERNAME_OWNER = '<EMAIL>';

    private const array USERS = [
        [
            'email' => self::USERNAME_LICENSEE,
            'firstname' => 'Tommy',
            'lastname' => 'the Test Licensee',
            'defaultScope' => UserScope::SCOPE_LICENSEE,
            'verified' => true,
            'password' => self::USER_PASSWORD,
        ],
        [
            'email' => self::USERNAME_LICENSEE_MISSING_COMPANY,
            'firstname' => 'Licensee Company',
            'lastname' => 'Missing',
            'defaultScope' => UserScope::SCOPE_LICENSEE,
            'verified' => true,
            'password' => self::USER_PASSWORD,
        ],
        [
            'email' => self::USERNAME_LICENSEE_MISSING_PROFILE,
            'firstname' => 'Licensee Profile',
            'lastname' => 'Missing',
            'defaultScope' => UserScope::SCOPE_LICENSEE,
            'verified' => true,
            'password' => self::USER_PASSWORD,
        ],
        [
            'email' => self::USERNAME_LICENSOR,
            'firstname' => 'Leo',
            'lastname' => 'the Test Licensor',
            'defaultScope' => UserScope::SCOPE_LICENSOR,
            'verified' => true,
            'password' => self::USER_PASSWORD,
            'licensor' => 'test-licensor-company',
        ],
        [
            'email' => self::USERNAME_LICENSOR_MISSING_COMPANY,
            'firstname' => 'Licensor Company',
            'lastname' => 'Missing',
            'defaultScope' => UserScope::SCOPE_LICENSEE,
            'verified' => true,
            'password' => self::USER_PASSWORD,
        ],
        [
            'email' => self::USERNAME_LICENSOR_MISSING_PROFILE,
            'firstname' => 'Licensor Profile',
            'lastname' => 'Missing',
            'defaultScope' => UserScope::SCOPE_LICENSOR,
            'verified' => true,
            'password' => self::USER_PASSWORD,
        ],

        [
            'email' => self::USERNAME_INVESTOR,
            'firstname' => 'Ivan',
            'lastname' => 'the Test Investor',
            'defaultScope' => UserScope::SCOPE_INVESTOR,
            'verified' => true,
            'password' => self::USER_PASSWORD,
            'investor' => 'test-investor-company',
        ],
        [
            'email' => self::USERNAME_INVESTOR_MISSING_COMPANY,
            'firstname' => 'Investor Company',
            'lastname' => 'Missing',
            'defaultScope' => UserScope::SCOPE_LICENSEE,
            'verified' => true,
            'password' => self::USER_PASSWORD,
        ],
        [
            'email' => self::USERNAME_INVESTOR_MISSING_PROFILE,
            'firstname' => 'Investor Profile',
            'lastname' => 'Missing',
            'defaultScope' => UserScope::SCOPE_LICENSEE,
            'verified' => true,
            'password' => self::USER_PASSWORD,
        ],

        [
            'email' => self::USERNAME_UNVERIFIED,
            'firstname' => 'Username',
            'lastname' => 'Unverified',
            'defaultScope' => null,
            'verified' => false,
            'password' => self::USER_PASSWORD,
        ],
        [
            'email' => self::USERNAME_MISSING_SCOPE,
            'firstname' => 'Scope',
            'lastname' => 'Missing',
            'defaultScope' => null,
            'verified' => true,
            'password' => self::USER_PASSWORD,
        ],
        [
            'email' => self::USERNAME_ADMIN,
            'firstname' => 'Awesome',
            'lastname' => 'Admin',
            'defaultScope' => null,
            'verified' => true,
            'password' => self::USER_PASSWORD,
            'role' => UserRole::ROLE_ADMIN,
        ],
        [
            'email' => self::USERNAME_OWNER,
            'firstname' => 'Awesome',
            'lastname' => 'Owner',
            'defaultScope' => null,
            'verified' => true,
            'password' => self::USER_PASSWORD,
            'role' => UserRole::ROLE_OWNER,
        ],
    ];

    public function __construct(
        private readonly UserPasswordHasherInterface $passwordHasher,
    ) {
    }

    public function load(ObjectManager $manager): void
    {
        foreach (self::USERS as $userData) {
            $user = new User();
            $user->email = $userData['email'];
            $user->firstName = $userData['firstname'];
            $user->lastName = $userData['lastname'];
            $user->defaultScope = $userData['defaultScope'];
            $user->isVerified = $userData['verified'];
            $user->password = $this->passwordHasher->hashPassword(
                $user,
                $userData['password']
            );
            $user->role = $userData['role'] ?? null;

            $manager->persist($user);
            $this->addToRepository($user->email, $user);
        }

        $manager->flush();
    }

    public function getDependencies(): array
    {
        return [
            CountryFixtures::class,
        ];
    }

    public static function getGroups(): array
    {
        return ['tests'];
    }

}
