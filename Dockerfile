#syntax=docker/dockerfile:1@sha256:9857836c9ee4268391bb5b09f9f157f3c91bb15821bb77969642813b0d00518d

# Versions
FROM dunglas/frankenphp:1-php8.4@sha256:fb8be3d3433f13b555eb6bebe33f2019f0361d3e540f95762d3d1c8efa8d1235 AS frankenphp_upstream

# The different stages of this Dockerfile are meant to be built into separate images
# https://docs.docker.com/develop/develop-images/multistage-build/#stop-at-a-specific-build-stage
# https://docs.docker.com/compose/compose-file/#target


# Base FrankenPHP image
FROM frankenphp_upstream AS frankenphp_base

WORKDIR /app

VOLUME /app/var/

# persistent / runtime deps
# hadolint ignore=DL3008
RUN apt-get update && apt-get install -y --no-install-recommends \
	acl \
	file \
	gettext \
	git \
    npm \
	&& rm -rf /var/lib/apt/lists/*

# Add non-root user for better security
ARG USER=appuser
RUN useradd ${USER} && \
    # Remove default capability
    setcap -r /usr/local/bin/frankenphp && \
    # Give write access to Caddy directories
    mkdir -p /data/caddy /config/caddy && \
    chown -R ${USER}:${USER} /data/caddy && \
    chown -R ${USER}:${USER} /config/caddy && \
    chown -R ${USER}:${USER} /app

RUN set -eux; \
	install-php-extensions \
		@composer \
		apcu \
		intl \
		opcache \
		zip \
        amqp \
        pdo_mysql \
        sockets \
        opentelemetry \
        protobuf \
        mbstring \
        grpc \
        zlib \
        bcmath \
	;

# https://getcomposer.org/doc/03-cli.md#composer-allow-superuser
ENV COMPOSER_ALLOW_SUPERUSER=1

ENV PHP_INI_SCAN_DIR=":$PHP_INI_DIR/app.conf.d"

# Set SERVER_NAME to use unprivileged port
ENV SERVER_NAME=":8080"

###> recipes ###
###< recipes ###

COPY --link frankenphp/conf.d/10-app.ini $PHP_INI_DIR/app.conf.d/
COPY --link frankenphp/Caddyfile /etc/caddy/Caddyfile

# Switch to non-root user
USER ${USER}

HEALTHCHECK --start-period=60s CMD curl -f http://localhost:2019/metrics || exit 1
CMD [ "frankenphp", "run", "--config", "/etc/caddy/Caddyfile" ]

# Dev FrankenPHP image
FROM frankenphp_base AS frankenphp_dev
USER root

ENV APP_ENV=dev XDEBUG_MODE=off

RUN mv "$PHP_INI_DIR/php.ini-development" "$PHP_INI_DIR/php.ini"

RUN set -eux; \
	install-php-extensions \
		xdebug \
	;

COPY --link frankenphp/conf.d/20-app.dev.ini $PHP_INI_DIR/app.conf.d/

# Switch to non-root user
USER ${USER}

CMD [ "frankenphp", "run", "--config", "/etc/caddy/Caddyfile", "--watch" ]

# Prod FrankenPHP image
FROM frankenphp_base AS frankenphp_prod
USER root

ENV APP_ENV=prod

RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

COPY --link frankenphp/conf.d/20-app.prod.ini $PHP_INI_DIR/app.conf.d/

# prevent the reinstallation of vendors at every changes in the source code
COPY --link composer.* symfony.* ./
RUN set -eux; \
    cat composer.json && \
    composer config --json extra.symfony.docker 'true' && \
	composer install --no-cache --prefer-dist --no-dev --no-autoloader --no-scripts --no-progress --no-interaction

# copy sources
COPY --link . ./
RUN rm -Rf frankenphp/

RUN set -eux; \
	mkdir -p var/cache var/log; \
    npm install; \
	composer dump-autoload --classmap-authoritative --no-dev; \
	composer dump-env prod; \
	composer run-script --no-dev post-install-cmd; \
    npm run build; \
    php bin/console cache:clear --env=prod; \
    php bin/console ux:icons:warm-cache; \
    php bin/console cache:warmup; \
    php bin/console assets:install --symlink --relative; \
	chmod +x bin/console; \
	chown -R ${USER}:${USER} /app; \
    setfacl -R -m u:www-data:rwX -m u:"${USER}":rwX var; \
    setfacl -dR -m u:www-data:rwX -m u:"${USER}":rwX var; \
    sync;

# Switch back to non-root user for runtime
USER ${USER}
