<?php

namespace App\Controller\Onboarding;

use App\Entity\User;
use App\Entity\UserScope;
use App\Form\Onboarding\CreateBrandFormType;
use App\Form\Onboarding\CreateLicenseeStep1FormType;
use App\Form\Onboarding\CreateLicenseeStep2FormType;
use App\Form\Onboarding\CreateLicensorFormType;
use App\Security\Voter\UserHasCompanyVoter;
use App\Security\Voter\UserHasScopeVoter;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authorization\Voter\AuthenticatedVoter;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted(AuthenticatedVoter::IS_AUTHENTICATED)]
#[IsGranted(UserHasScopeVoter::class)]
#[IsGranted(UserHasCompanyVoter::class)]
class CreateProfileController extends AbstractController
{

    #[Route('/onboarding/create_profile/step/{step}', name: 'app_onboarding_createprofile')]
    public function createProfile(int $step, UrlGeneratorInterface $urlGenerator): Response
    {
        /** @var User $user */
        $user = $this->getUser();

        $scope = $user->defaultScope;
        $backLink = $step > 1
            ? $urlGenerator->generate('app_onboarding_createprofile', ['step' => $step - 1])
            : $urlGenerator->generate('app_onboarding_createcompany');

        return $this->render(
            'pages/onboarding/create_profile.html.twig',
            [
                'scope' => $scope,
                'formType' => $this->getFormType($scope, $step),
                'backLink' => $backLink,
                'step' => $step,
            ]
        );
    }

    #[Route('/onboarding/create_profile/success', name: 'app_onboarding_createprofile_success')]
    public function success(): Response
    {
        return $this->render('pages/onboarding/create_profile_success.html.twig');
    }

    private function getFormType(UserScope $scope, int $step): ?string
    {
        if (UserScope::SCOPE_LICENSEE === $scope) {
            return match ($step) {
                1 => CreateLicenseeStep1FormType::class,
                2 => CreateLicenseeStep2FormType::class,
                default => null,
            };
        }

        if (UserScope::SCOPE_LICENSOR === $scope) {
            return match ($step) {
                1 => CreateLicensorFormType::class,
                2 => CreateBrandFormType::class,
                default => null,
            };
        }

        // Investor scope will be handled later
        return null;
    }

}
