<?php

declare(strict_types=1);

namespace App\Twig\Components\Pages\Onboarding;

use App\Entity\Company;
use App\Entity\User;
use App\Entity\UserCompanyRelation;
use App\Entity\UserRole;
use App\Form\Onboarding\CompanyFormType;
use App\Service\UploadService;
use Cocur\Slugify\Slugify;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authorization\Voter\AuthenticatedVoter;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\Attribute\LiveAction;
use Symfony\UX\LiveComponent\Attribute\LiveProp;
use Symfony\UX\LiveComponent\ComponentWithFormTrait;
use Symfony\UX\LiveComponent\DefaultActionTrait;

#[AsLiveComponent('pages:onboarding:create-company', template: 'components/layout/form.html.twig')]
#[IsGranted(AuthenticatedVoter::IS_AUTHENTICATED)]
final class CreateCompany extends AbstractController
{
    use DefaultActionTrait;
    use ComponentWithFormTrait;

    #[LiveProp]
    public ?Company $company = null;

    protected function instantiateForm(): FormInterface
    {
        if ($this->company === null) {
            /** @var User $user */
            $user = $this->getUser();
            $this->company = $user->companies->first() ?: new Company();
        }

        return $this->createForm(CompanyFormType::class, $this->company);
    }

    #[LiveAction]
    public function save(
        Request                $request,
        EntityManagerInterface $entityManager,
        UploadService          $uploadService
    ): RedirectResponse {
        // Submit the form! If validation fails, an exception is thrown
        $this->submitForm();

        $entityManager->beginTransaction();
        try {
            $company = $this->getForm()->getData();
            /* @var Company $company */
            $company->slug = new Slugify()->slugify($company->name);
            if (!$company->id) {
                $entityManager->persist($company);
                // Need an id for file upload
                $entityManager->flush();
            }

            $uploadedLogo = $request->files->get('company_form')['logoFile'] ?? null;
            if ($uploadedLogo) {
                $folder = 'company/'.$company->id;
                $fileLocation = $uploadService->uploadFile(
                    file: $uploadedLogo,
                    folder: $folder,
                    fileName: 'logo',
                    replace: true
                );
                /* @var UploadedFile $uploadedLogo */
                $company->logo = $fileLocation;
            }

            /** @var User $user */
            $user = $this->getUser();
            if (!$user->companies->contains($company)) {
                $userRelation = new UserCompanyRelation();
                $userRelation->company = $company;
                $userRelation->role = UserRole::ROLE_OWNER;
                $userRelation->user = $user;
                $entityManager->persist($userRelation);
            }

            $entityManager->flush();
            $entityManager->commit();
        } catch (\Throwable $e) {
            $entityManager->rollback();
            throw $e;
        }

        return $this->redirectToRoute('app_onboarding_createprofile', ['step' => 1]);
    }

}
