<?php

namespace App\Repository;

use App\DTO\Licensor\BrandListFilter;
use App\Entity\Brand;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Brand>
 */
class BrandRepository extends ServiceEntityRepository
{

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Brand::class);
    }

    public function findBrandsByFilter(BrandListFilter $filter): QueryBuilder
    {
        $qb = $this->createQueryBuilder('b');
        $qb
            ->innerJoin('b.licensor', 'l')
            ->innerJoin('l.company', 'c')
            ->innerJoin('b.targetAudienceGenders', 'tag')
            ->innerJoin('b.targetAudienceAgeGroups', 'taag')
            ->innerJoin('l.productCategories', 'pc')
            ->innerJoin('l.territories', 't')
            ->innerJoin('b.ipCategories', 'ipc')
            ->select('b, l, c');

        if ($filter->ipCategories) {
            $qb->andWhere($qb->expr()->in('ipc.slug', ':ipCategories'));
            $qb->setParameter('ipCategories', $filter->ipCategories);
        }

        if ($filter->productCategories) {
            $qb->andWhere($qb->expr()->in('pc.slug', ':productCategories'));
            $qb->setParameter('ipCategories', $filter->productCategories);
        }

        if ($filter->territories) {
            $qb->andWhere($qb->expr()->in('t.slug', ':territories'));
            $qb->setParameter('territories', $filter->territories);
        }

        if ($filter->targetAudienceGenders) {
            $qb->andWhere($qb->expr()->in('tag.slug', ':targetAudienceGenders'));
            $qb->setParameter('targetAudienceGenders', $filter->targetAudienceGenders);
        }

        if ($filter->targetAudienceAgeGroups) {
            $qb->andWhere($qb->expr()->in('taag.slug', ':targetAudienceAgeGroups'));
            $qb->setParameter('targetAudienceAgeGroups', $filter->targetAudienceAgeGroups);
        }

        if ($filter->search) {
            $qb->andWhere($qb->expr()->like('b.name', ':search'))
                ->setParameter('search', '%'.$filter->search.'%');
        }

        return $qb;
    }

    /**
     * @param int[] $notThese
     */
    public function findADifferentBrand(array $notThese): ?Brand
    {
        $qb = $this->createQueryBuilder('b');
        if (!empty($notThese)) {
            $qb->andWhere($qb->expr()->notIn('b.id', ':notThese'));
            $qb->setParameter('notThese', $notThese);
        }

        return $qb->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

}
