<?php

declare(strict_types=1);

namespace App\Twig\Runtime;

use App\DTO\Licensee\LicenseeListFilter;
use App\Service\LicensorService;
use Twig\Extension\RuntimeExtensionInterface;

class LicenseeListExtensionRuntime implements RuntimeExtensionInterface
{

    public function __construct(private readonly LicensorService $licensorService)
    {
    }

    public function getLicenseeListFilter(): LicenseeListFilter
    {
        return $this->licensorService->getListFilter();
    }

}
