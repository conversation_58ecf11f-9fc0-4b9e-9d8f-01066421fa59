<?php

namespace App\Form\Onboarding;

use App\Entity\Company;
use App\Entity\Country;
use App\Form\Type\ImageType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\UrlType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CompanyFormType extends AbstractType
{

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Company::class,
        ]);
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        // TODO: Organize Form components in layout / pages architecture
        // Issue URL: https://github.com/TedaTech/tedaconnect-marketplace/issues/82
        $builder
            ->add('name', TextType::class, [
                'required' => true,
                'label' => 'Company Name',
                'attr' => ['placeholder' => 'Enter your company name'],
            ])
            ->add('website', UrlType::class, [
                'required' => true,
                'default_protocol' => 'https',
                'label' => 'Company website URL',
                'attr' => ['placeholder' => 'https://example.com'],
            ])
            ->add('description', TextareaType::class, [
                'required' => true,
                'label' => 'Company Description',
                'attr' => [
                    'placeholder' => 'Give us a description of what your company is all about',
                    'rows' => 5,
                ],
            ])
            ->add('logoFile', ImageType::class, [
                'required' => false,
                'label' => 'Company Logo',
                'mapped' => false,
                'multiple' => false,
                'help' => 'Upload a logo for your company. This will be visible for brands to see.',
            ])
            ->add('country', EntityType::class, [
                'required' => true,
                'class' => Country::class,
                'label' => 'Country',
                'data' => 'USA',
                'choice_label' => 'name',
                'choice_value' => 'isoCode',
                'help' => 'Where are you situated?',
            ])
            ->add('submit', SubmitType::class, [
                'label' => 'Continue',
                'attr' => [
                    'class' => 'btn btn-primary',
                    'data-action' => 'pages:onboarding:create-company#save',
                ],
            ]);
    }

}
