{% set title = title|default('') %}
{% set iconClass = iconClass|default('h-8 text-primary') %}
<div data-controller="popover" {{ attributes }}>
    <a data-action="mouseenter->popover#show mouseleave->popover#hide" class="relative z-20 transition-opacity group">
        <twig:ux:icon
            name="mdi:alert-circle-outline"
            class="{{ iconClass }} group-hover:hidden"
            title="{{ title }}"
        />
        <twig:ux:icon
            name="mdi:alert-circle"
            class="{{ iconClass }} hidden group-hover:inline"
            title="{{ title }}"
        />
    </a>

    <template data-popover-target="content">
        <div
                data-popover-target="card"
                class="max-w-sm rounded shadow-lg bg-white absolute left-0 bottom-0"
                style="bottom: 2rem; width: max-content"
        >

        </div>

        <div
            role="tooltip"
            class="card-popup w-2xs absolute left-0 bottom-0 px-3 py-2 z-100 transition-opacity duration-300 tooltip"
            style="bottom: 2rem; width: max-content"
        >
            {% block content %}{% endblock %}
        </div>
    </template>
</div>
