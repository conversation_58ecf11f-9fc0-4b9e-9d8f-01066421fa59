<?php

declare(strict_types=1);

namespace App\Twig\Components\Pages\Licensor;

use App\Entity\Brand;
use App\Entity\Licensee;
use App\Entity\Project;
use App\Entity\UserScope;
use App\Interfaces\MatchItemInterface;
use App\Repository\LicenseeMatchViewRepository;
use App\Service\FavoriteService;
use App\Service\LicenseeService;
use App\Service\LicensorService;
use App\Service\Match\MatchFinderInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\Attribute\LiveAction;
use Symfony\UX\LiveComponent\Attribute\LiveArg;
use Symfony\UX\LiveComponent\DefaultActionTrait;

#[AsLiveComponent(name: 'pages:licensor:brand-match')]
class BrandMatch extends AbstractController
{
    use DefaultActionTrait;

    public function __construct(
        private readonly LicenseeService             $licenseeService,
        private readonly LicensorService             $licensorService,
        private readonly EntityManagerInterface      $entityManager,
        private readonly MatchFinderInterface        $matchService,
        private readonly RequestStack                $requestStack,
        private readonly LicenseeMatchViewRepository $licenseeMatchViewRepository,
        private readonly UrlGeneratorInterface       $urlGenerator,
        private readonly FavoriteService             $favoriteService,
    ) {
    }

    public function get(): MatchItemInterface
    {
        $index = $this->getIndex();
        $visited = $this->getVisits();

        if (isset($visited[$index])) {
            $type = $visited[$index]['type'];
            $itemId = $visited[$index]['itemId'];

            /*
             * TODO: Possible error on session / entity database changes
             *   Whenever the database entities change a user can generate an exception:
             *   An exception has been thrown during the rendering of a template ("Entity of type 'App\Entity\Project'
             *   for IDs id(75) was not found") in components/pages/licensor/brand-match.html.twig at line 8.
             */
            return $this->entityManager->find($type, $itemId);
        }

        return $this->matchService->findNext($this->getBaseItem());
    }

    public function getType(MatchItemInterface $matchItem): string
    {
        return match (true) {
            $matchItem instanceof Project => Project::class,
            $matchItem instanceof Brand => Brand::class,
            $matchItem instanceof Licensee => Licensee::class,
        };
    }

    public function getDetailUrl(MatchItemInterface $matchItem): string
    {
        return match ($this->getType($matchItem)) {
            Project::class => $this->urlGenerator
                ->generate('app_licensor_project_details', ['slug' => $matchItem->getSlug()]),
            Brand::class => $this->urlGenerator
                ->generate('app_licensor_brand_details', ['slug' => $matchItem->getSlug()]),
            Licensee::class => throw new \Exception('NOT YET IMPLEMENTED')
        };
    }

    public function getIndex(): int
    {
        $index = $this->requestStack->getSession()->get('brand-match-index', 0);
        if (!is_numeric($index)) {
            $index = 0;
        }

        return (int) $index;
    }

    #[LiveAction]
    public function prev(): void
    {
        $this->updateIndex($this->getIndex() - 1);
    }

    #[LiveAction]
    public function next(#[LiveArg] int $itemId, #[LiveArg] string $itemType): void
    {
        $this->save($itemId, $itemType);
        $this->updateIndex($this->getIndex() + 1);
    }

    #[LiveAction]
    public function favorite(#[LiveArg] int $itemId, #[LiveArg] string $itemType): void
    {
        switch ($itemType) {
            case Project::class:
                $matchItem = $this->entityManager->find(Project::class, $itemId);
                $this->favoriteService->addLicenseeFavoriteProject($this->getBaseItem(), $matchItem);
                break;
            case Brand::class:
                $matchItem = $this->entityManager->find(Brand::class, $itemId);
                $this->favoriteService->addLicenseeFavoriteBrand($this->getBaseItem(), $matchItem);
                break;
            default:
                throw new \Exception('NOT IMPLEMENTED');
        }

        $this->next($itemId, $itemType);
    }

    public function getBaseItem(): MatchItemInterface
    {
        /** @var \App\Entity\User $user */
        $user = $this->getUser();

        return match ($user->defaultScope) {
            UserScope::SCOPE_LICENSEE => $this->licenseeService->find(),
            UserScope::SCOPE_LICENSOR => $this->licensorService->find(),
        };
    }

    private function save(int $itemId, string $itemType): void
    {
        if ($this->getIndex() >= count($this->getVisits())) {
            $this->addVisit($itemId, $itemType);
            $this->licenseeMatchViewRepository->saveVisit(
                $itemId,
                $itemType,
                $this->licenseeService->find()
            );
        }
    }

    private function addVisit(int $itemId, string $itemType): void
    {
        $visited = $this->getVisits();
        $visited[] = ['itemId' => $itemId, 'type' => $itemType];
        $this->requestStack->getSession()->set('brand-match-visited', $visited);
    }

    /**
     * @return array<int, array{itemId: int, type: string}>
     */
    private function getVisits(): array
    {
        $visited = $this->requestStack->getSession()->get('brand-match-visited', []);
        if (!is_array($visited)) {
            $visited = [];
        }

        return $visited;
    }

    private function updateIndex(int $index): void
    {
        $this->requestStack->getSession()->set('brand-match-index', max(0, $index));
    }

}
