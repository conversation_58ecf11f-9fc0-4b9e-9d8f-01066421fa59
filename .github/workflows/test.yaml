# Schema: https://json.schemastore.org/github-action.json
name: test
on:
  pull_request:
    branches:
      - main

permissions:
  contents: read
  pull-requests: write
  checks: write

jobs:
  test:
    runs-on: tedatech
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      - run: devbox run test

  lint:
    runs-on: tedatech
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      - run: devbox run lint
