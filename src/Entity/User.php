<?php

namespace App\Entity;

use App\Repository\UserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Security\Core\User\EquatableInterface;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: '`user`')]
#[ORM\UniqueConstraint(name: 'UNIQ_IDENTIFIER_EMAIL', fields: ['email'])]
#[UniqueEntity(fields: ['email'], message: 'There is already an account with this email')]
class User implements UserInterface, PasswordAuthenticatedUserInterface, EquatableInterface
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    public int $id;

    #[ORM\Column(length: 180)]
    public string $email;

    #[ORM\Column(length: 180, nullable: true)]
    public ?string $newEmail = null;

    #[ORM\Column]
    public string $firstName;

    #[ORM\Column]
    public string $lastName;

    #[ORM\Column]
    public string $password;

    #[ORM\Column]
    public bool $isVerified = false;

    #[ORM\Column(nullable: true)]
    public ?UserScope $defaultScope = null;

    /**
     * The role of the user in the context of the application, not the role of the user in the context of a company.
     */
    #[ORM\Column(nullable: true)]
    public ?UserRole $role = null;

    /**
     * @var Collection<int, UserCompanyRelation>
     */
    #[ORM\OneToMany(targetEntity: UserCompanyRelation::class, mappedBy: 'user', fetch: 'EAGER')]
    public Collection $companyRelations;

    /**
     * @var Collection<int, Company>
     */
    public Collection $companies {
        get => $this->companyRelations->map(fn (UserCompanyRelation $relation) => $relation->company);
    }

    public function __construct()
    {
        $this->companyRelations = new ArrayCollection();
    }

    /**
     * @see UserInterface
     *
     * @return list<string>
     */
    public function getRoles(): array
    {
        $roles = $this->companyRelations
            ->map(static fn (UserCompanyRelation $relation) => $relation->company->id.'_'.$relation->role->value)
            ->toArray();

        if ($this->role) {
            $roles[] = $this->role->value;
        }

        return $roles;
    }

    /**
     * @see UserInterface
     */
    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
        // $this->plainPassword = null;
    }

    public function getUserIdentifier(): string
    {
        return $this->email;
    }

    /**
     * @see PasswordAuthenticatedUserInterface
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    /**
     * @see \Symfony\Component\Security\Http\Firewall\ContextListener::hasUserChanged()
     */
    public function isEqualTo(UserInterface $user): bool
    {
        if (!$user instanceof self) {
            return false;
        }

        if ($this->getUserIdentifier() !== $user->getUserIdentifier()) {
            return false;
        }

        if ($this->getPassword() !== $user->getPassword()) {
            return false;
        }

        /**
         * When the user gained roles re authentication is not needed
         * The original checks if there are any differences in the roles and forces re authentication if this is the case.
         * When a company user relation is created, a role is added to the user, this is no reason to re authenticate.
         * The case where a role is removed should be handled correctly.
         *
         * @see \Symfony\Component\Security\Http\Firewall\ContextListener::hasUserChanged()
         */
        $myRoles = $this->getRoles();

        return array_intersect($myRoles, $user->getRoles()) === $myRoles;
    }

}
