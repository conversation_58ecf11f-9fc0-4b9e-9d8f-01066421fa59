# Schema: https://json.schemastore.org/github-action.json
name: todo
on:
  push:
    branches:
      - main
      - test
jobs:
  todo:
    runs-on: ubuntu-latest
    timeout-minutes: 2
    permissions:
      contents: write
      issues: write
      pull-requests: write
    steps:
      - uses: "actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683" # v4
      - uses: "alstr/todo-to-issue-action@3bd536e14a2cbceeab1fadef96bea5f725ed4270" # v5
        with:
          INSERT_ISSUE_URLS: "true"
      - uses: "stefanzweifel/git-auto-commit-action@b863ae1933cb653a53c021fe36dbb774e1fb9403" # v5
