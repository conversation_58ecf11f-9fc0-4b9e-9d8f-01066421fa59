<?php

declare(strict_types=1);

namespace App\Form\Licensee;

use App\DTO\Licensee\LicenseeListFilter;
use App\Entity\AgeGroup;
use App\Entity\DistributionChannel;
use App\Entity\Gender;
use App\Entity\ProductCategory;
use App\Entity\Territory;
use App\Service\EntityToOptionsService;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SearchType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class LicenseeFilterFormType extends AbstractType
{

    public function __construct(
        private readonly EntityToOptionsService $entityToOptionsService,
    ) {
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => LicenseeListFilter::class,
        ]);
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('distributionChannels', ChoiceType::class, [
                'multiple' => true,
                'choices' => $this->entityToOptionsService->getAllEntityOptions(DistributionChannel::class),
                'autocomplete' => true,
                'label' => 'Distribution channels',
                'required' => false,
            ])
            ->add('territories', ChoiceType::class, [
                'multiple' => true,
                'choices' => $this->entityToOptionsService->getAllEntityOptions(Territory::class),
                'autocomplete' => true,
                'label' => 'Territories',
                'required' => false,
            ])
            ->add('productCategories', ChoiceType::class, [
                'multiple' => true,
                'choices' => $this->entityToOptionsService->getAllEntityOptions(ProductCategory::class),
                'autocomplete' => true,
                'label' => 'Product categories',
                'required' => false,
            ])
            ->add('targetAudienceAgeGroups', ChoiceType::class, [
                'multiple' => true,
                'choices' => $this->entityToOptionsService->getAllEntityOptions(AgeGroup::class),
                'autocomplete' => true,
                'label' => 'Age Groups',
                'required' => false,
            ])
            ->add('targetAudienceGenders', ChoiceType::class, [
                'multiple' => true,
                'choices' => $this->entityToOptionsService->getAllEntityOptions(Gender::class),
                'autocomplete' => true,
                'label' => 'Genders',
                'required' => false,
            ])
            ->add('search', SearchType::class, [
                'required' => false,
            ]);
    }

}
