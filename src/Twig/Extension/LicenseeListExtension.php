<?php

declare(strict_types=1);

namespace App\Twig\Extension;

use App\Twig\Runtime\LicenseeListExtensionRuntime;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

class LicenseeListExtension extends AbstractExtension
{

    public function getFunctions(): array
    {
        return [
            new TwigFunction('licensee_filter', [LicenseeListExtensionRuntime::class, 'getLicenseeListFilter']),
        ];
    }

}
