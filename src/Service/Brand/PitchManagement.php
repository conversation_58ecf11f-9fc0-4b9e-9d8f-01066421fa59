<?php

declare(strict_types=1);

namespace App\Service\Brand;

use App\DTO\Licensor\BrandPitchDTO;
use App\Entity\AgeGroup;
use App\Entity\Brand;
use App\Entity\DistributionChannel;
use App\Entity\Gender;
use App\Entity\Licensee;
use App\Entity\ProductCategory;
use App\Entity\Project;
use App\Entity\ProjectBid;
use App\Entity\ProjectBidRevision;
use App\Entity\ProjectBidStatus;
use App\Entity\Territory;
use App\Service\LicenseeService;
use Cocur\Slugify\Slugify;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;

readonly class PitchManagement
{

    public function __construct(
        private EntityManagerInterface $entityManager,
        private LicenseeService        $licenseeService,
    ) {
    }

    public function canPitchBrand(Licensee $licensee, Brand $brand): bool
    {
        // TODO(@edwinljacobs): Brand pitch restrictions
        // Issue URL: https://github.com/TedaTech/tedaconnect-marketplace/issues/190
        //  Under which conditions can a licensee pitch a brand?
        //  Should we enforce some limits? Is a brand open for pitches? Can they manage that themselves?
        return true;
    }

    public function saveBrandPitch(BrandPitchDTO $brandPitchDTO): void
    {
        /** @var Brand $brand */
        $brand = $this->entityManager->getRepository(Brand::class)->find($brandPitchDTO->brandId);
        $licensee = $this->licenseeService->find();
        $this->entityManager->beginTransaction();
        try {
            // Create the project
            $project = new Project();
            $project->brand = $brand;
            $project->name = $brandPitchDTO->projectName;
            $project->description = $brandPitchDTO->projectOverview;
            $project->currency = $brandPitchDTO->currency;
            $project->slug = new Slugify()->slugify(
                sprintf('%s %s', $licensee->getSlug(), $brandPitchDTO->projectName)
            );
            $project->public = false;

            $project->productCategories = new ArrayCollection(
                $this->entityManager
                    ->getRepository(ProductCategory::class)
                    ->findBy(['slug' => $brandPitchDTO->productCategories])
            );

            $project->territories = new ArrayCollection(
                $this->entityManager
                    ->getRepository(Territory::class)
                    ->findBy(['slug' => $brandPitchDTO->territories])
            );

            $project->distributionChannels = new ArrayCollection(
                $this->entityManager
                    ->getRepository(DistributionChannel::class)
                    ->findBy(['slug' => $brandPitchDTO->distributionChannels])
            );

            $project->targetAudienceAgeGroups = new ArrayCollection(
                $this->entityManager
                    ->getRepository(AgeGroup::class)
                    ->findBy(['slug' => $brandPitchDTO->targetAudienceAgeGroups])
            );

            $project->targetAudienceGenders = new ArrayCollection(
                $this->entityManager
                ->getRepository(Gender::class)
                ->findBy(['slug' => $brandPitchDTO->targetAudienceGenders])
            );

            $project->minimumGuaranteeMin = $brandPitchDTO->proposedMinimumGuarantee;
            $project->royaltyRateMin = $brandPitchDTO->proposedRoyaltyRate;
            $this->entityManager->persist($project);
            $brand->projects->add($project);
            $this->entityManager->flush();

            // Create the corresponding project bid
            $projectBid = new ProjectBid();
            $projectBid->project = $project;
            $projectBid->licensee = $licensee;
            $projectBid->status = ProjectBidStatus::PENDING;
            $this->entityManager->persist($projectBid);

            // Create the project initial bid revision
            $bidRevision = new ProjectBidRevision();
            $bidRevision->projectBid = $projectBid;
            $bidRevision->projectOverview = $brandPitchDTO->projectOverview;
            $bidRevision->revisionOwner = $projectBid->licensee->company;
            $bidRevision->proposedMinimumGuarantee = $brandPitchDTO->proposedMinimumGuarantee;
            $bidRevision->proposedRoyaltyRate = $brandPitchDTO->proposedRoyaltyRate;
            $bidRevision->launchDate = $brandPitchDTO->launchDate;
            // Bid revision has a production timeline but this is not available in the DTO (it is not in the field list)

            $this->entityManager->persist($bidRevision);
            $this->entityManager->flush();
            $this->entityManager->commit();
        } catch (\Throwable $e) {
            $this->entityManager->rollback();
            throw $e;
        }
    }

}
