<?php

namespace App\Repository;

use App\DTO\Licensor\ProjectListFilter;
use App\Entity\Project;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Project>
 */
class ProjectRepository extends ServiceEntityRepository
{

    public function __construct(
        ManagerRegistry $registry
    ) {
        parent::__construct($registry, Project::class);
    }

    /**
     * @return Project[]
     */
    public function findProjectsByFilter(ProjectListFilter $filter): array
    {
        $qb = $this->initProjectListQueryBuilder();

        if ($filter->brands) {
            $qb->andWhere($qb->expr()->in('brand.slug', ':brands'));
            $qb->setParameter('brands', $filter->brands);
        }

        if ($filter->productCategories) {
            $qb->andWhere($qb->expr()->in('projectProductCategories.slug', ':categories'));
            $qb->setParameter('categories', $filter->productCategories);
        }

        if ($filter->territories) {
            $qb->andWhere($qb->expr()->in('projectTerritories.slug', ':territories'));
            $qb->setParameter('territories', $filter->territories);
        }

        if ($filter->targetAudienceGenders) {
            $qb->andWhere($qb->expr()->in('projectTargetAudienceGenders.slug', ':genders'));
            $qb->setParameter('genders', $filter->targetAudienceGenders);
        }

        if ($filter->targetAudienceAgeGroups) {
            $qb->andWhere($qb->expr()->in('projectTargetAudienceAgeGroups.slug', ':ageGroups'));
            $qb->setParameter('ageGroups', $filter->targetAudienceAgeGroups);
        }

        if ($filter->distributionChannels) {
            $qb->andWhere($qb->expr()->in('projectDistributionChannels.slug', ':distributionChannels'));
            $qb->setParameter('distributionChannels', $filter->distributionChannels);
        }

        if ($filter->search) {
            $qb->andWhere($qb->expr()->like('project.name', ':search'));
            $qb->setParameter('search', '%'.$filter->search.'%');
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Returns a list of projects that are similar to the given projects.
     * These include the projects themselves and are not ordered or grouped by anything.
     * A project is in the resultset if it is similar to at least one of the given projects.
     *
     * @param array<int, Project> $projects
     *
     * @return array<int, Project>
     */
    public function getSimilarProjectsForProjectList(array $projects): array
    {
        $ipCategories = new ArrayCollection();
        $productCategories = new ArrayCollection();
        $genders = new ArrayCollection();
        $ageGroups = new ArrayCollection();

        foreach ($projects as $project) {
            foreach ($project->brand->ipCategories as $ipCategory) {
                $ipCategories->offsetSet($ipCategory->id, $ipCategory);
            }

            foreach ($project->productCategories as $productCategory) {
                $productCategories->offsetSet($productCategory->id, $productCategory);
            }

            foreach ($project->targetAudienceGenders as $gender) {
                $genders->offsetSet($gender->id, $gender);
            }

            foreach ($project->targetAudienceAgeGroups as $ageGroup) {
                $ageGroups->offsetSet($ageGroup->id, $ageGroup);
            }
        }

        $qb = $this->initProjectListQueryBuilder();
        $qb->innerJoin('brand.ipCategories', 'brandIpCategories');
        $qb->addSelect('brandIpCategories');

        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->in('brandIpCategories', ':ipCategories'),
                $qb->expr()->in('projectProductCategories', ':productCategories'),
                $qb->expr()->in('projectTargetAudienceGenders', ':genders'),
                $qb->expr()->in('projectTargetAudienceAgeGroups', ':ageGroups')
            )
        );

        $qb->setParameter('ipCategories', $ipCategories);
        $qb->setParameter('productCategories', $productCategories);
        $qb->setParameter('genders', $genders);
        $qb->setParameter('ageGroups', $ageGroups);

        return $qb->getQuery()->getResult();
    }

    /**
     * This is a direct call to the database to get similar projects for a single project.
     *
     * @return array<int, Project>
     */
    public function getSimilarProjectsForProject(Project $project): array
    {
        $qb = $this->initProjectListQueryBuilder();
        $qb->innerJoin('brand.ipCategories', 'brandIpCategories');
        $qb->addSelect('brandIpCategories');

        $qb->andWhere($qb->expr()->neq('project.id', ':id'));
        $qb->setParameter('id', $project->id);

        $qb->andWhere(
            $qb->expr()->orX(
                $qb->expr()->in('brandIpCategories.slug', ':ipCategories'),
                $qb->expr()->in('projectProductCategories.slug', ':productCategories'),
                $qb->expr()->andX(
                    $qb->expr()->in('projectTargetAudienceGenders.slug', ':genders'),
                    $qb->expr()->in('projectTargetAudienceAgeGroups.slug', ':ageGroups')
                )
            )
        );

        $qb->setParameter('ipCategories', $this->slugify($project->brand->ipCategories));
        $qb->setParameter('productCategories', $this->slugify($project->productCategories));
        $qb->setParameter('genders', $this->slugify($project->targetAudienceGenders));
        $qb->setParameter('ageGroups', $this->slugify($project->targetAudienceAgeGroups));

        return $qb->getQuery()->getResult();
    }

    /**
     * @param int[] $notTheseProjects
     */
    public function findADifferentProject(array $notTheseProjects): ?Project
    {
        $qb = $this->initProjectListQueryBuilder();
        if (!empty($notTheseProjects)) {
            $qb->andWhere($qb->expr()->notIn('project.id', ':notTheseProjects'));
            $qb->setParameter('notTheseProjects', $notTheseProjects);
        }

        $qb->setMaxResults(1);

        $results = $qb->getQuery()->getResult();

        if (count($results) > 0) {
            return $results[0];
        }

        return null;
    }

    private function initProjectListQueryBuilder(): QueryBuilder
    {
        $qb = $this->createQueryBuilder('project');
        $qb
            ->innerJoin('project.brand', 'brand')
            ->innerJoin('brand.licensor', 'licensor')
            ->innerJoin('licensor.company', 'company')
            ->leftJoin('project.productCategories', 'projectProductCategories')
            ->leftJoin('project.territories', 'projectTerritories')
            ->leftJoin('project.distributionChannels', 'projectDistributionChannels')
            ->leftJoin('project.targetAudienceGenders', 'projectTargetAudienceGenders')
            ->leftJoin('project.targetAudienceAgeGroups', 'projectTargetAudienceAgeGroups')
            ->select([
                'project',
                'brand',
                'licensor',
                'company',
            ]);
        // Only display public projects
        $qb->andWhere($qb->expr()->eq('project.public', ':public'));
        $qb->setParameter('public', true);
        // Only display projects that are available for bidding
        $qb->andWhere($qb->expr()->eq('project.bidEnabled', ':bidEnabled'));
        $qb->setParameter('bidEnabled', true);

        return $qb;
    }

    /**
     * @param Collection $collection collection of items which have a slug attribute
     *
     * @return string[]
     */
    private function slugify(Collection $collection): array
    {
        return $collection->map(static fn ($item) => $item->slug)->toArray();
    }

}
