<?php

declare(strict_types=1);

namespace App\Twig\Components\Pages\Licensor;

use App\Entity\Brand;
use App\Service\FavoriteService;
use App\Service\LicenseeService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\Attribute\LiveAction;
use Symfony\UX\LiveComponent\Attribute\LiveProp;
use Symfony\UX\LiveComponent\DefaultActionTrait;

#[AsLiveComponent(name: 'pages:licensor:brand-favorite-button', template: 'components/layout/favorite-button.html.twig')]
class BrandFavoriteButton
{
    use DefaultActionTrait;

    #[LiveProp]
    public int $brandId;

    public function __construct(
        private readonly FavoriteService        $favoriteService,
        private readonly LicenseeService        $licenseeService,
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    #[LiveAction]
    public function toggle(): void
    {
        $this->favoriteService->toggleLicenseeFavoriteBrand(
            $this->licenseeService->find(),
            $this->getBrand(),
        );
    }

    public function isFavorite(): bool
    {
        return $this->favoriteService->isBrandFavoriteForLicensee(
            $this->licenseeService->find(),
            $this->getBrand()
        );
    }

    private function getBrand(): Brand
    {
        return $this->entityManager->find(Brand::class, $this->brandId);
    }

}
