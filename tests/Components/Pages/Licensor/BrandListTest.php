<?php

declare(strict_types=1);

namespace App\Tests\Components\Pages\Licensor;

use App\DataFixtures\Tests\TestUserFixtures;
use App\DTO\Licensor\BrandListFilter;
use App\Tests\AppWebTestCase;
use App\Tests\TestSelectors;

class BrandListTest extends AppWebTestCase
{

    public function testBrandListComponent(): void
    {
        /** @var \Symfony\Bundle\FrameworkBundle\KernelBrowser $client */
        $client = static::createClient();
        $component = $this->setupLiveComponent(
            client: $client,
            componentName: 'pages:licensor:brand-list',
            userName: TestUserFixtures::USERNAME_LICENSEE
        );

        $crawler = $component->render()->crawler();
        $listItems = $crawler->filter(TestSelectors::BRAND_LIST_ITEM);
        self::assertCount(12, $listItems);

        // Check if the brand list can be searched
        $filter = new BrandListFilter();
        $filter->search = 'Looney Tunes';

        $component = $this->setupLiveComponent(
            client: $client,
            componentName: 'pages:licensor:brand-list',
            componentData: ['filter' => $filter],
            userName: TestUserFixtures::USERNAME_LICENSEE
        );

        $crawler = $component->render()->crawler();
        /** @noinspection CssInvalidFunction */
        $listItems = $crawler->filter('span:contains("Looney Tunes")');
        self::assertCount(1, $listItems);

        // Check if the brand list can be filtered by IP category
    }

}
