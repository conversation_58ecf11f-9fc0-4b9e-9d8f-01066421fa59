{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.4", "ext-amqp": "*", "ext-ctype": "*", "ext-iconv": "*", "cocur/slugify": "^4.6", "doctrine/dbal": "^3.9.4", "doctrine/doctrine-bundle": "^2.14.0", "doctrine/doctrine-fixtures-bundle": "^4.1", "doctrine/doctrine-migrations-bundle": "^3.4.2", "doctrine/orm": "^3.3.3", "knplabs/knp-paginator-bundle": "^6.8.1", "league/commonmark": "^2.7.0", "league/flysystem-memory": "^3.29", "liip/monitor-bundle": "^2.23.2", "longitude-one/doctrine-spatial": "^5.0.3", "oneup/flysystem-bundle": "^4.12.4", "open-telemetry/exporter-otlp": "^1.3.1", "open-telemetry/opentelemetry-auto-doctrine": "^0.2.1", "open-telemetry/opentelemetry-auto-ext-amqp": "^0.0.6", "open-telemetry/opentelemetry-auto-symfony": "^1.0.1", "pentatrion/vite-bundle": "^8.1.0", "php-amqplib/php-amqplib": "^3.7.3", "runtime/frankenphp-symfony": "^0.2.0", "sentry/sentry-symfony": "^5.2", "sonata-project/admin-bundle": "^4.36.2", "sonata-project/doctrine-orm-admin-bundle": "^4.18", "symfony/amqp-messenger": "7.2.*", "symfony/console": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/expression-language": "7.2.*", "symfony/flex": "^2.7.1", "symfony/form": "7.2.*", "symfony/framework-bundle": "7.2.*", "symfony/http-client": "7.2.*", "symfony/http-foundation": "7.2.*", "symfony/intl": "7.2.*", "symfony/mailer": "7.2.*", "symfony/mailtrap-mailer": "7.2.*", "symfony/messenger": "7.2.*", "symfony/monolog-bundle": "^3.10", "symfony/property-access": "7.2.*", "symfony/runtime": "7.2.*", "symfony/security-bundle": "7.2.*", "symfony/serializer": "7.2.*", "symfony/stimulus-bundle": "^2.25.2", "symfony/twig-bundle": "7.2.*", "symfony/ux-autocomplete": "^2.25.2", "symfony/ux-dropzone": "^2.25.2", "symfony/ux-icons": "^2.25", "symfony/ux-live-component": "^2.25.2", "symfony/ux-turbo": "^2.25.2", "symfony/validator": "7.2.*", "symfony/yaml": "7.2.*", "symfonycasts/verify-email-bundle": "^1.17.3", "twig/extra-bundle": "^3.21", "twig/intl-extra": "^3.21", "twig/markdown-extra": "^3.21", "webmozart/assert": "^1.11"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true, "tbachert/spi": true}, "bump-after-update": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}, "hooks": {"config": {"stop-on-failure": ["pre-commit"]}, "pre-commit": [".githooks/pre-commit-php-cs-fixer"]}}, "require-dev": {"brainmaestro/composer-git-hooks": "^3.0", "dama/doctrine-test-bundle": "^8.3.0", "erickskrauch/php-cs-fixer-custom-fixers": "^1.3", "friendsofphp/php-cs-fixer": "^3.75", "phpstan/phpstan": "^2.1.17", "phpunit/phpunit": "^12.1.6", "symfony/browser-kit": "7.2.*", "symfony/css-selector": "7.2.*", "symfony/debug-bundle": "7.2.*", "symfony/maker-bundle": "^1.63.0", "symfony/phpunit-bridge": "^7.2.6", "symfony/stopwatch": "7.2.*", "symfony/web-profiler-bundle": "7.2.*"}}