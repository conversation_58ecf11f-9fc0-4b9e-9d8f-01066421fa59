<?php

declare(strict_types=1);

namespace App\Tests\Controller\Onboarding;

use App\DataFixtures\Tests\TestUserFixtures;
use App\Tests\AppWebTestCase;

class CreateProfileControllerTest extends AppWebTestCase
{

    public function testCreateProfilePage(): void
    {
        $client = static::createClient();
        $client->request('GET', '/onboarding/create_profile/step/1');
        self::assertResponseRedirects('/login');

        $this->loginUser($client, TestUserFixtures::USERNAME_LICENSEE_MISSING_COMPANY);
        $client->request('GET', '/onboarding/create_profile/step/1');
        self::assertResponseRedirects('/onboarding/create_company');

        $this->loginUser($client, TestUserFixtures::USERNAME_LICENSEE_MISSING_PROFILE);
        $client->request('GET', '/onboarding/create_profile/step/1');
        self::assertResponseIsSuccessful();
        // Test that this is indeed the licensee create form page
        self::assertSelectorExists('[name="create_licensee_step1_form"]');

        $this->loginUser($client, TestUserFixtures::USERNAME_LICENSOR_MISSING_PROFILE);
        $client->request('GET', '/onboarding/create_profile/step/1');
        self::assertResponseIsSuccessful();
        // Test that this is indeed the licensor create page
        self::assertSelectorExists('[name="create_licensor_form"]');
    }

}
