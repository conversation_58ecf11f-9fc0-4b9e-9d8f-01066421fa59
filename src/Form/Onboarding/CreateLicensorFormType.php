<?php

declare(strict_types=1);

namespace App\Form\Onboarding;

use App\Entity\DistributionChannel;
use App\Entity\Licensor;
use App\Entity\ProductCategory;
use App\Entity\Territory;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CreateLicensorFormType extends AbstractType
{

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Licensor::class,
        ]);
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('productCategories', EntityType::class, [
            'required' => true,
            'multiple' => true,
            'autocomplete' => true,
            'class' => ProductCategory::class,
            'choice_label' => 'name',
            'label' => 'Product categories',
            'help' => 'Select one or more product categories you\'re looking for.',
        ]);

        $builder->add('territories', EntityType::class, [
            'multiple' => true,
            'required' => true,
            'autocomplete' => true,
            'class' => Territory::class,
            'choice_label' => 'name',
            'label' => 'Territories',
            'help' => 'Select the territories where you\'re offering licensing rights for.',
        ]);

        $builder->add('distributionChannels', EntityType::class, [
            'multiple' => true,
            'required' => true,
            'autocomplete' => true,
            'class' => DistributionChannel::class,
            'choice_label' => 'name',
            'label' => 'Preferred distribution channels',
            'help' => 'Select the distribution channels you expect or prefer licensees to use.',
        ]);

        $builder->add('submit', SubmitType::class, [
            'label' => 'Continue',
            'attr' => [
                'class' => 'btn btn-primary',
            ],
        ]);
    }

}
