<?php

declare(strict_types=1);

namespace App\Exception;

use League\Flysystem\FilesystemException;

class FileUploadException extends \Exception
{

    public static function createMissingId(mixed $object): self
    {
        return new self(sprintf(
            'Cannot resolve object upload directory for object with type: %s, object does not have an id',
            get_class($object)
        ));
    }

    public static function createFromFilesystemException(FilesystemException $e): self
    {
        return new self(
            'Something went wrong while uploading the file. Details: '.$e->getMessage(),
            code: $e->getCode(),
            previous: $e
        );
    }

}
