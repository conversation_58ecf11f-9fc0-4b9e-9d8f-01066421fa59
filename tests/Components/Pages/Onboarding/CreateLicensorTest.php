<?php

declare(strict_types=1);

namespace App\Tests\Components\Pages\Onboarding;

use App\DataFixtures\Tests\TestUserFixtures;
use App\Entity\AgeGroup;
use App\Entity\Company;
use App\Entity\DistributionChannel;
use App\Entity\Gender;
use App\Entity\IPCategory;
use App\Entity\Licensor;
use App\Entity\ProductCategory;
use App\Entity\Territory;
use App\Form\Onboarding\CreateBrandFormType;
use App\Form\Onboarding\CreateLicensorFormType;
use App\Tests\AppWebTestCase;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class CreateLicensorTest extends AppWebTestCase
{

    public function testCreateLicensorForm()
    {
        $client = static::createClient();
        $entityManager = $client->getContainer()->get(EntityManagerInterface::class);

        // STEP 1
        $component = $this->setupLiveComponent(
            $client,
            'pages:onboarding:create-licensor',
            [
                'formType' => CreateLicensorFormType::class,
                'step' => 1,
            ],
            TestUserFixtures::USERNAME_LICENSOR_MISSING_PROFILE
        );

        $component->render();
        self::assertSelectorExists('[name="create_licensor_form[productCategories][]"]');
        self::assertSelectorExists('[name="create_licensor_form[territories][]"]');
        self::assertSelectorExists('[name="create_licensor_form[distributionChannels][]"]');

        $productCategory = $entityManager->getRepository(ProductCategory::class)->findOneBy([]);
        $distributionChannel = $entityManager->getRepository(DistributionChannel::class)->findOneBy([]);
        $territory = $entityManager->getRepository(Territory::class)->findOneBy([]);

        $component->set('create_licensor_form.productCategories', [$productCategory->id]);
        $component->set('create_licensor_form.distributionChannels', [$distributionChannel->id]);
        $component->set('create_licensor_form.territories', [$territory->id]);
        $component->call('save');

        self::assertResponseRedirects('/onboarding/create_profile/step/2');

        // STEP 2
        $component = $this->setupLiveComponent(
            $client,
            'pages:onboarding:create-brand',
            [
                'formType' => CreateBrandFormType::class,
                'step' => 2,
            ],
            TestUserFixtures::USERNAME_LICENSOR_MISSING_PROFILE
        );

        $component->render();
        self::assertSelectorExists('[name="create_brand_form[name]"]');
        self::assertSelectorExists('[name="create_brand_form[description]"]');
        self::assertSelectorExists('[name="create_brand_form[logoFile]"]');
        self::assertSelectorExists('[name="create_brand_form[heroImageFile]"]');
        self::assertSelectorExists('[name="create_brand_form[ipCategories][]"]');
        self::assertSelectorExists('[name="create_brand_form[targetAudience][ageGroups][]"]');
        self::assertSelectorExists('[name="create_brand_form[targetAudience][genders][]"]');
        self::assertSelectorExists('[name="create_brand_form[preferredMinimumGuarantee][min]"]');
        self::assertSelectorExists('[name="create_brand_form[preferredMinimumGuarantee][max]"]');
        self::assertSelectorExists('[name="create_brand_form[preferredRoyaltyRate][min]"]');
        self::assertSelectorExists('[name="create_brand_form[preferredRoyaltyRate][max]"]');

        $ipCategory = $entityManager->getRepository(IPCategory::class)->findOneBy([]);
        $ageGroup = $entityManager->getRepository(AgeGroup::class)->findOneBy([]);
        $gender = $entityManager->getRepository(Gender::class)->findOneBy([]);

        $component->set('create_brand_form.name', 'Test Case Brand Name');
        $component->set('create_brand_form.description', 'Test Case Brand Description');
        $component->set('create_brand_form.logo', 'Test Case Brand Logo');
        $component->set('create_brand_form.heroImage', 'Test Case Brand HeroImage');
        $component->set('create_brand_form.ipCategories', [$ipCategory->id]);
        $component->set('create_brand_form.targetAudience.ageGroups', [$ageGroup->id]);
        $component->set('create_brand_form.targetAudience.genders', [$gender->id]);
        $component->set('create_brand_form.preferredMinimumGuarantee.min', 75000);
        $component->set('create_brand_form.preferredMinimumGuarantee.max', 250000);
        $component->set('create_brand_form.preferredRoyaltyRate.min', 10);
        $component->set('create_brand_form.preferredRoyaltyRate.max', 20);

        $projectDir = $client->getKernel()->getProjectDir();
        $uploadedLogo = new UploadedFile(
            $projectDir.'/tests/assets/test-upload-logo.jpg',
            'test-upload-logo.jpg'
        );
        $uploadedHeroImage = new UploadedFile(
            $projectDir.'/tests/assets/test-upload-hero-image.jpg',
            'test-upload-hero-image.jpg'
        );

        /* @noinspection PhpParamsInspection */
        $component->call(
            action: 'save',
            files: [
                'create_brand_form' => [
                    'logoFile' => $uploadedLogo,
                    'heroImageFile' => $uploadedHeroImage,
                ],
            ]
        );

        self::assertResponseRedirects('/onboarding/create_profile/success');

        // Check if licensor was created with correct values
        $licensor = $this->findLicensor($entityManager);
        self::assertCount(1, $licensor->productCategories);
        self::assertEquals($productCategory->id, $licensor->productCategories->first()->id);

        self::assertCount(1, $licensor->distributionChannels);
        self::assertEquals($distributionChannel->id, $licensor->distributionChannels->first()->id);

        self::assertCount(1, $licensor->territories);
        self::assertEquals($territory->id, $licensor->territories->first()->id);

        // Check if brand was created with correct values
        $brand = $licensor->brands->first();
        self::assertNotEmpty($brand);
        self::assertEquals('Test Case Brand Name', $brand->name);
        self::assertEquals('Test Case Brand Description', $brand->description);
        // self::assertEquals('Test Case Brand Logo', $brand->logo);
        // self::assertEquals('Test Case Brand HeroImage', $brand->heroImage);
        self::assertEquals(75000, $brand->preferredMinimumGuaranteeMin);
        self::assertEquals(250000, $brand->preferredMinimumGuaranteeMax);
        self::assertEquals(10, $brand->preferredRoyaltyRateMin);
        self::assertEquals(20, $brand->preferredRoyaltyRateMax);

        self::assertCount(1, $brand->ipCategories);
        self::assertEquals($ipCategory->id, $brand->ipCategories->first()->id);

        self::assertCount(1, $brand->targetAudienceAgeGroups);
        self::assertEquals($ageGroup->id, $brand->targetAudienceAgeGroups->first()->id);

        self::assertCount(1, $brand->targetAudienceGenders);
        self::assertEquals($gender->id, $brand->targetAudienceGenders->first()->id);

        self::assertNotEmpty($brand->logo);
        self::assertNotEmpty($brand->heroImage);
        $fileSystem = $client->getContainer()->get('oneup_flysystem.user_uploads_filesystem');
        self::assertTrue($fileSystem->has($brand->logo));
        self::assertTrue($fileSystem->has($brand->heroImage));
    }

    private function findLicensor(EntityManagerInterface $entityManager): ?Licensor
    {
        $company = $entityManager->getRepository(Company::class)
            ->findOneBy(['slug' => 'test-company-missing-profile']);

        return $entityManager->getRepository(Licensor::class)
            ->findOneBy(['company' => $company]);
    }

}
