{"dama/doctrine-test-bundle": {"version": "8.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "7.2", "ref": "896306d79d4ee143af9eadf9b09fd34a8c391b70"}, "files": ["config/packages/dama_doctrine_test_bundle.yaml"]}, "doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "2.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "8d96c0b51591ffc26794d865ba3ee7d193438a83"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "4.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "friendsofphp/php-cs-fixer": {"version": "3.69", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "knplabs/knp-menu-bundle": {"version": "v3.5.0"}, "knplabs/knp-paginator-bundle": {"version": "v6.8.0"}, "liip/monitor-bundle": {"version": "2.23", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.6", "ref": "02c7cd93304dead890f9042fc2670dd9b0e0c091"}, "files": ["config/packages/monitor.yaml"]}, "oneup/flysystem-bundle": {"version": "4.12", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "3ae1b83985e89138f5443bbc2d9b8c074b497d49"}, "files": ["config/packages/oneup_flysystem.yaml"]}, "pentatrion/vite-bundle": {"version": "8.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "6.5", "ref": "3a6673f248f8fc1dd364dadfef4c5b381d1efab6"}}, "php-http/discovery": {"version": "1.20", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.18", "ref": "f45b5dd173a27873ab19f5e3180b2f661c21de02"}, "files": ["config/packages/http_discovery.yaml"]}, "phpstan/phpstan": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}, "files": ["phpstan.dist.neon"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "sentry/sentry-symfony": {"version": "5.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "5.0", "ref": "76afa07d23e76f678942f00af5a6a417ba0816d0"}, "files": ["config/packages/sentry.yaml"]}, "sonata-project/admin-bundle": {"version": "4.36", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "0e5931df1732e3dccfba42a20853049e5e9db6ae"}, "files": ["config/packages/sonata_admin.yaml", "config/routes/sonata_admin.yaml", "src/Admin/.gitignore"]}, "sonata-project/block-bundle": {"version": "5.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.11", "ref": "b4edd2a1e6ac1827202f336cac2771cb529de542"}, "files": ["config/packages/sonata_block.yaml"]}, "sonata-project/doctrine-extensions": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.8", "ref": "4ea4a4b6730f83239608d7d4c849533645c70169"}}, "sonata-project/doctrine-orm-admin-bundle": {"version": "4.18.0"}, "sonata-project/exporter": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.4", "ref": "93d6df022ef1dc24bdfa8667ddd560bbde89a7cc"}}, "sonata-project/form-extensions": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.4", "ref": "9c8a1e8ce2b1f215015ed16652c4ed18eb5867fd"}, "files": ["config/packages/sonata_form.yaml"]}, "sonata-project/twig-extensions": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.2", "ref": "30dba2f9b719f21b497a6302f41aac07f9079e13"}}, "symfony/console": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["bin/console"]}, "symfony/debug-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["config/packages/debug.yaml"]}, "symfony/flex": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "52e9754527a15e2b79d9a610f98185a1fe46622a"}, "files": [".env", ".env.dev"]}, "symfony/form": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "7d86a6723f4a623f59e2bf966b6aad2fc461d36b"}, "files": ["config/packages/csrf.yaml"]}, "symfony/framework-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "87bcf6f7c55201f345d8895deda46d2adbdbaa89"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/mailer": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "09051cfde49476e3c12cd3a0e44289ace1c75a4f"}, "files": ["config/packages/mailer.yaml"]}, "symfony/mailtrap-mailer": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "e1d25e0fab8778b977233aff6762d3791eab3155"}}, "symfony/maker-bundle": {"version": "1.62", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["config/packages/monolog.yaml"]}, "symfony/phpunit-bridge": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "a411a0480041243d97382cac7984f7dce7813c08"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/routing": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "21b72649d5622d8f7da329ffb5afb232a023619d"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["config/packages/security.yaml", "config/routes/security.yaml"]}, "symfony/stimulus-bundle": {"version": "2.23", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.20", "ref": "3acc494b566816514a6873a89023a35440b6386d"}, "files": ["assets/bootstrap.js", "assets/controllers.json", "assets/controllers/csrf_protection_controller.js", "assets/controllers/hello_controller.js"]}, "symfony/translation": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "e28e27f53663cc34f0be2837aba18e3a1bef8e7b"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/twig-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/ux-autocomplete": {"version": "2.23", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.6", "ref": "07d9602b7231ba355f484305d6cea58310c01741"}, "files": ["config/routes/ux_autocomplete.yaml"]}, "symfony/ux-dropzone": {"version": "v2.25.2"}, "symfony/ux-icons": {"version": "2.23", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.17", "ref": "803a3bbd5893f9584969ab8670290cdfb6a0a5b5"}, "files": ["assets/icons/symfony.svg"]}, "symfony/ux-live-component": {"version": "2.23", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.6", "ref": "73e69baf18f47740d6f58688c5464b10cdacae06"}, "files": ["config/routes/ux_live_component.yaml"]}, "symfony/ux-turbo": {"version": "2.23", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.20", "ref": "c85ff94da66841d7ff087c19cbcd97a2df744ef9"}}, "symfony/ux-twig-component": {"version": "2.23", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "67814b5f9794798b885cec9d3f48631424449a01"}, "files": ["config/packages/twig_component.yaml"]}, "symfony/validator": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "8c1c4e28d26a124b0bb273f537ca8ce443472bfd"}, "files": ["config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "e42b3f0177df239add25373083a564e5ead4e13a"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "symfonycasts/verify-email-bundle": {"version": "v1.17.3"}, "twig/extra-bundle": {"version": "v3.20.0"}}