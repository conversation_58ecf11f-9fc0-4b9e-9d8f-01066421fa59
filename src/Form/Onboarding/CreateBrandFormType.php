<?php

declare(strict_types=1);

namespace App\Form\Onboarding;

use App\Entity\Brand;
use App\Entity\IPCategory;
use App\Form\Type\ImageType;
use App\Form\Type\PreferredMinimumGuaranteeType;
use App\Form\Type\PreferredRoyaltyRateType;
use App\Form\Type\TargetAudienceType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\DataMapperInterface;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CreateBrandFormType extends AbstractType implements DataMapperInterface
{

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Brand::class,
        ]);
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add('name', TextType::class, [
            'required' => true,
            'label' => 'Brand Name',
            'help' => 'You can have more than one IP - no worries, you can add all of them later in your profile page.',
        ]);

        $builder->add('logoFile', ImageType::class, [
            'required' => false,
            'label' => 'Brand Logo',
            'mapped' => false,
            'multiple' => false,
            'help' => 'Upload your brand logo. This will appear on your marketplace profile and in search results.',
        ]);

        $builder->add('heroImageFile', ImageType::class, [
            'required' => false,
            'label' => 'Hero Image',
            'mapped' => false,
            'multiple' => false,
            'help' => 'Add a banner or hero image to visually showcase your brand.',
        ]);

        $builder->add('ipCategories', EntityType::class, [
            'required' => false,
            'multiple' => true,
            'autocomplete' => true,
            'class' => IPCategory::class,
            'choice_label' => 'name',
            'label' => 'Brand Category',
            'help' => 'Select the category that best represents your brand or IP.',
        ]);

        $builder->add('targetAudience', TargetAudienceType::class, [
            'label' => 'Target audience',
            'help' => 'Who is your brand primarily intended for?',
            'mapped' => false,
        ]);

        $builder->add('description', TextareaType::class, [
            'required' => false,
            'label' => 'Description',
            'attr' => [
                'placeholder' => 'Describe your brand. What’s your story, and what makes your IP unique?',
                'rows' => 5,
            ],
        ]);

        $builder->add('preferredMinimumGuarantee', PreferredMinimumGuaranteeType::class, [
            'required' => true,
            'label' => 'Preferred Minimum Guarantee',
            'help' => 'What is your preferred minimum guarantee? Use a range or a fixed value. If you leave the upper range empty, it will be treated as a fixed MG.',
            'mapped' => false,
        ]);

        $builder->add('preferredRoyaltyRate', PreferredRoyaltyRateType::class, [
            'required' => true,
            'label' => 'Preferred Royalty Rate (%)',
            'help' => 'What is your preferred royalty rate from product sales? Enter a percentage range or a fixed value. Leaving the upper field empty will treat it as a fixed offer.',
            'mapped' => false,
        ]);

        $builder->add('submit', SubmitType::class, [
            'label' => 'Continue',
            'attr' => [
                'class' => 'btn btn-primary mt-4',
            ],
        ]);

        $builder->setDataMapper($this);
    }

    /**
     * @param Brand|null $viewData
     */
    public function mapDataToForms(mixed $viewData, \Traversable $forms): void
    {
        // there is no data yet, so nothing to prepopulate
        if ($viewData === null) {
            return;
        }

        /** @var FormInterface[] $forms */
        $forms = iterator_to_array($forms);

        // initialize form field values

        $forms['name']->setData($viewData->name);
        // $forms['logoFile']->setData($viewData->logo);
        // $forms['heroImage']->setData($viewData->heroImage);
        $forms['description']->setData($viewData->description);
        $forms['ipCategories']->setData($viewData->ipCategories);

        $forms['targetAudience']->setData(
            [
                'ageGroups' => $viewData->targetAudienceAgeGroups,
                'genders' => $viewData->targetAudienceGenders,
            ]
        );

        $forms['preferredMinimumGuarantee']->setData(
            [
                'min' => $viewData->preferredMinimumGuaranteeMin,
                'max' => $viewData->preferredMinimumGuaranteeMax,
            ]
        );

        $forms['preferredRoyaltyRate']->setData(
            [
                'min' => $viewData->preferredRoyaltyRateMin,
                'max' => $viewData->preferredRoyaltyRateMax,
            ]
        );
    }

    /**
     * @param Brand $viewData
     */
    public function mapFormsToData(\Traversable $forms, mixed &$viewData): void
    {
        $forms = iterator_to_array($forms);

        $viewData->name = $forms['name']->getData();
        // $viewData->logo         = $forms['logoFile']->getData();
        // $viewData->heroImage    = $forms['heroImage']->getData();
        $viewData->description = $forms['description']->getData();
        $viewData->ipCategories = $forms['ipCategories']->getData();

        $minimumGuaranteeMin = $forms['preferredMinimumGuarantee']['min']->getData();
        $minimumGuaranteeMax = $forms['preferredMinimumGuarantee']['max']->getData();
        $viewData->preferredMinimumGuaranteeMin = $minimumGuaranteeMin ? (int) $minimumGuaranteeMin : null;
        $viewData->preferredMinimumGuaranteeMax = $minimumGuaranteeMax ? (int) $minimumGuaranteeMax : null;

        $royaltyRateMin = $forms['preferredRoyaltyRate']['min']->getData();
        $royaltyRateMax = $forms['preferredRoyaltyRate']['max']->getData();
        $viewData->preferredRoyaltyRateMin = $royaltyRateMin ? (string) $royaltyRateMin : null;
        $viewData->preferredRoyaltyRateMax = $royaltyRateMax ? (string) $royaltyRateMax : null;

        $viewData->targetAudienceAgeGroups = $forms['targetAudience']['ageGroups']->getData();
        $viewData->targetAudienceGenders = $forms['targetAudience']['genders']->getData();
    }

}
