<?php

declare(strict_types=1);

namespace App\Twig\Components\Pages\Licensor;

use App\Entity\Project;
use App\Service\FavoriteService;
use App\Service\LicenseeService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\Attribute\LiveAction;
use Symfony\UX\LiveComponent\Attribute\LiveProp;
use Symfony\UX\LiveComponent\DefaultActionTrait;

#[AsLiveComponent(name: 'pages:licensor:project-favorite-button', template: 'components/layout/favorite-button.html.twig')]
class ProjectFavoriteButton
{
    use DefaultActionTrait;

    #[LiveProp]
    public int $projectId;

    public function __construct(
        private readonly FavoriteService        $favoriteService,
        private readonly LicenseeService        $licenseeService,
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    #[LiveAction]
    public function toggle(): void
    {
        $this->favoriteService->toggleLicenseeFavoriteProject(
            $this->licenseeService->find(),
            $this->getProject(),
        );
    }

    public function isFavorite(): bool
    {
        return $this->favoriteService->isProjectFavoriteForLicensee(
            $this->licenseeService->find(),
            $this->getProject()
        );
    }

    private function getProject(): Project
    {
        return $this->entityManager->find(Project::class, $this->projectId);
    }

}
