<?php

declare(strict_types=1);

namespace App\Tests\Controller\Licensor;

use App\DataFixtures\Tests\TestUserFixtures;
use App\Tests\AppWebTestCase;
use App\Tests\TestSelectors;

class BrandDetailsControllerTest extends AppWebTestCase
{

    public function testBrandDetailsPageAsLicensee(): void
    {
        $client = self::createClient();
        $this->testIfPageIsSuccessful(
            $client,
            '/licensor/brand/frozen',
            TestUserFixtures::USERNAME_LICENSEE
        );

        // Brand page should have a dashboard link
        self::assertSelectorExists(TestSelectors::LICENSEE_DASHBOARD_LINK);
        // Brand page should have a pitch button
        self::assertSelectorExists(TestSelectors::BRAND_PITCH_BUTTON);
        // Brand page should have a link to the brand's website
        self::assertSelectorExists(TestSelectors::BRAND_WEBSITE_LINK);
        // Brand page should have a list of projects, this depends on the brand, but in this case it does
        self::assertSelectorExists(TestSelectors::BRAND_PROJECT_LIST_ITEM);

        // Brand page should display the following data as badge lists
        // 1) Preferred product categories
        // 2) Territories
        // 3) Distribution channels
        // 4) Audience age groups
        // 5) Audience genders

        // Verify that the badge lists are present
        $detailsBadgeListsSelector = TestSelectors::BRAND_DETAILS_CONTENT.' '.TestSelectors::BADGE_LIST;
        self::assertSelectorCount(5, $detailsBadgeListsSelector);

        // Verify that the badge lists contain the expected data (just based on title for now)
        $detailsContentH2Selector = TestSelectors::BRAND_DETAILS_CONTENT.' h2';
        $detailsContentH3Selector = TestSelectors::BRAND_DETAILS_CONTENT.' h3';
        self::assertAnySelectorTextContains($detailsContentH2Selector, 'Preferred product categories');
        self::assertAnySelectorTextContains($detailsContentH2Selector, 'Territories');
        self::assertAnySelectorTextContains($detailsContentH2Selector, 'Distribution channels');
        self::assertAnySelectorTextContains($detailsContentH2Selector, 'Audience');
        self::assertAnySelectorTextContains($detailsContentH3Selector, 'Age groups');
        self::assertAnySelectorTextContains($detailsContentH3Selector, 'Genders');
    }

    public function testBrandDetailsPageAsAnonymous(): void
    {
        $client = self::createClient();
        $this->testIfPageRedirects(
            $client,
            '/licensor/brand/frozen',
            '/login'
        );
    }

}
