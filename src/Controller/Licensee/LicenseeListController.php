<?php

declare(strict_types=1);

namespace App\Controller\Licensee;

use App\Security\Voter\UserEmailVerifiedVoter;
use App\Security\Voter\UserHasLicensorProfileVoter;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Authorization\Voter\AuthenticatedVoter;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[IsGranted(AuthenticatedVoter::IS_AUTHENTICATED)]
#[IsGranted(UserEmailVerifiedVoter::class)]
#[IsGranted(UserHasLicensorProfileVoter::class)]
class LicenseeListController extends AbstractController
{

    #[Route(path: '/licensee/manufacturers', name: 'app_licensee_list')]
    public function index()
    {
        return $this->render('pages/licensee/licensee_list.html.twig');
    }

}
