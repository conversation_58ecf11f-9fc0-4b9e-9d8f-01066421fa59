# Schema: https://json.schemastore.org/github-action.json
name: setup-app
inputs:
  with-npm-cache:
    description: 'Configure NPM cache'
    required: false
    default: "false"
  with-composer-cache:
    description: 'Configure composer cache'
    required: false
    default: "true"
  with-services:
    description: 'Start docker services'
    required: false
    default: "false"
runs:
  using: 'composite'
  steps:
    - uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4
      if: ${{ inputs.enable-npm-cache == 'true' }}
      env:
        cache-name: cache-node-modules
      with:
        path: ~/.npm
        key: npm-${{ hashFiles('package-lock.json') }}
        restore-keys: npm-
    - id: "determine-composer-cache-directory"
      if: ${{ inputs.with-composer-cache == 'true' }}
      run: "echo \"directory=$(composer config cache-dir)\" >> $GITHUB_OUTPUT"
      shell: bash
    - uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4
      if: ${{ inputs.with-composer-cache == 'true' }}
      with:
        path: "${{ steps.determine-composer-cache-directory.outputs.directory }}"
        key: "php-composer-${{ hashFiles('composer.lock') }}"
        restore-keys: "php-composer-"
    - uses: jetify-com/devbox-install-action@734088efddca47cf44ff8a09289c6d0e51b73218 # v0.12.0
      with:
        enable-cache: 'true'
    - uses: hoverkraft-tech/compose-action@8be2d741e891ac9b8ac20825e6f3904149599925 # v2
      if: ${{ inputs.with-services == 'true' }}
      with:
        up-flags: --wait --quiet-pull --no-color
        compose-file: './compose.yaml'
