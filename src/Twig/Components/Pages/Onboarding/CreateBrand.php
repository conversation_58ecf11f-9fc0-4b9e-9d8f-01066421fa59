<?php

declare(strict_types=1);

namespace App\Twig\Components\Pages\Onboarding;

use App\Entity\Brand;
use App\Entity\User;
use App\Exception\AccessDeniedException;
use App\Exception\FileUploadException;
use App\Form\Onboarding\CreateBrandFormType;
use App\Security\Voter\UserEmailVerifiedVoter;
use App\Security\Voter\UserHasCompanyVoter;
use App\Security\Voter\UserHasLicensorProfileVoter;
use App\Security\Voter\UserHasScopeVoter;
use App\Service\ProfileService;
use App\Service\UploadService;
use Cocur\Slugify\Slugify;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authorization\Voter\AuthenticatedVoter;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\Attribute\LiveAction;
use Symfony\UX\LiveComponent\Attribute\LiveProp;
use Symfony\UX\LiveComponent\ComponentWithFormTrait;
use Symfony\UX\LiveComponent\DefaultActionTrait;

#[IsGranted(AuthenticatedVoter::IS_AUTHENTICATED)]
#[IsGranted(UserHasScopeVoter::class)]
#[IsGranted(UserEmailVerifiedVoter::class)]
#[IsGranted(UserHasCompanyVoter::class)]
#[IsGranted(UserHasLicensorProfileVoter::class)]
#[AsLiveComponent('pages:onboarding:create-brand', template: 'components/pages/onboarding/create-brand.html.twig')]
class CreateBrand extends AbstractController
{
    use DefaultActionTrait;
    use ComponentWithFormTrait;

    #[LiveProp]
    public ?Brand $brand = null;

    public function __construct(protected readonly ProfileService $profileService)
    {
    }

    protected function instantiateForm(): FormInterface
    {
        if ($this->brand === null) {
            /** @var User $user */
            $user = $this->getUser();
            $this->brand = $this->profileService->findLicensorByUser($user)?->brands->first() ?: new Brand();
        }

        return $this->createForm(CreateBrandFormType::class, $this->brand);
    }

    #[LiveAction]
    public function save(
        EntityManagerInterface $entityManager,
        Request                $request,
        UploadService          $uploadService,
        LoggerInterface        $logger
    ): RedirectResponse {
        $this->submitForm();
        /** @var Brand $brand */
        $brand = $this->getForm()->getData();

        if (!$brand->licensor) {
            /** @var User $user */
            $user = $this->getUser();
            $licensor = $this->profileService->findLicensorByUser($user);
            if (!$licensor) {
                throw new AccessDeniedException('app_onboarding_selectscope');
            }

            $brand->licensor = $licensor;
        }

        $brand->slug = new Slugify()->slugify($brand->name);

        if (!$brand->id) {
            // Need id for file upload
            $entityManager->persist($brand);
            $entityManager->flush();
        }

        try {
            $logoLocation = $this->uploadFile(
                uploadService: $uploadService,
                request: $request,
                brandId: $brand->id,
                fileKey: 'logoFile',
                fileName: 'logo'
            );
            $brand->logo = $logoLocation;

            $heroImageLocation = $this->uploadFile(
                uploadService: $uploadService,
                request: $request,
                brandId: $brand->id,
                fileKey: 'heroImageFile',
                fileName: 'hero-image'
            );
            $brand->heroImage = $heroImageLocation;
        } catch (FileUploadException $exception) {
            // This really should not happen, but since logo and hero image are optional, we do not bubble
            $logger->critical($exception);
            $this->addFlash('warning', 'File upload failed try again in your profile settings.');
        } finally {
            // Save the brand
            $entityManager->flush();
        }

        return $this->redirect($this->generateUrl('app_onboarding_createprofile_success'));
    }

    /**
     * @throws FileUploadException
     */
    private function uploadFile(
        UploadService $uploadService,
        Request       $request,
        int           $brandId,
        string        $fileKey,
        string        $fileName
    ): ?string {
        $folder = 'brand/'.$brandId;
        $uploadedFile = $request->files->get('create_brand_form')[$fileKey] ?? null;
        if ($uploadedFile) {
            return $uploadService->uploadFile(
                file: $uploadedFile,
                folder: $folder,
                fileName: $fileName,
                replace: true
            );
        }

        return null;
    }

}
