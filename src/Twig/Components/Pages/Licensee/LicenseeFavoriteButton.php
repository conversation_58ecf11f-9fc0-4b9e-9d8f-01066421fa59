<?php

declare(strict_types=1);

namespace App\Twig\Components\Pages\Licensee;

use App\Entity\Licensee;
use App\Service\FavoriteService;
use App\Service\LicensorService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\Attribute\LiveAction;
use Symfony\UX\LiveComponent\Attribute\LiveProp;
use Symfony\UX\LiveComponent\DefaultActionTrait;

#[AsLiveComponent(name: 'pages:licensee:favorite-button', template: 'components/layout/favorite-button.html.twig')]
class LicenseeFavoriteButton extends AbstractController
{
    use DefaultActionTrait;

    #[LiveProp]
    public int $licenseeId;

    public function __construct(
        private readonly FavoriteService        $favoriteService,
        private readonly LicensorService        $licensorService,
        private readonly EntityManagerInterface $entityManager,
    ) {
    }

    #[LiveAction]
    public function toggle(): void
    {
        $licensee = $this->getLicensee();
        if ($licensee === null) {
            return;
        }

        $this->favoriteService->toggleLicensorFavoriteLicensee(
            $this->licensorService->find(),
            $licensee,
        );
    }

    public function isFavorite(): bool
    {
        $licensee = $this->getLicensee();
        if ($licensee === null) {
            return false;
        }

        return $this->favoriteService->isLicenseeFavoriteForLicensor(
            $this->licensorService->find(),
            $licensee
        );
    }

    private function getLicensee(): ?Licensee
    {
        return $this->entityManager->find(Licensee::class, $this->licenseeId);
    }

}
