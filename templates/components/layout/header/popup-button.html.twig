<div data-controller="dropdown" class="relative">
    <button data-action="dropdown#toggle click@window->dropdown#hide">
        <twig:ux:icon
            name="{{ this.icon }}"
            class="{{ this.className }} icon"
        />
    </button>

    <div
        data-dropdown-target="menu"
        class="hidden transition transform origin-top-right absolute right-0 z-20"
        data-transition-enter-from="opacity-0 scale-95"
        data-transition-enter-to="opacity-100 scale-100"
        data-transition-leave-from="opacity-100 scale-100"
        data-transition-leave-to="opacity-0 scale-95"
    >
        {% block content %}{% endblock %}
    </div>
</div>
