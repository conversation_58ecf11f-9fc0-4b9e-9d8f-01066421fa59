<?php

namespace App\Entity;

use App\Interfaces\MatchItemInterface;
use App\Repository\ProjectRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ProjectRepository::class)]
#[ORM\Index(name: 'idx_project_name', fields: ['name'])]
#[ORM\UniqueConstraint('unq_project_slug', fields: ['slug'])]
class Project implements MatchItemInterface
{

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    public ?int $id = null;

    #[ORM\Column(length: 255)]
    public string $name;

    #[ORM\Column(type: 'text')]
    public string $description;

    #[ORM\Column(length: 255)]
    public string $slug;

    #[ORM\Column(type: 'boolean')]
    public bool $public = true;

    #[ORM\Column(type: 'boolean', nullable: false, options: ['default' => true])]
    public bool $bidEnabled = true;

    #[ORM\ManyToOne(targetEntity: Brand::class, inversedBy: 'projects')]
    #[ORM\JoinColumn(nullable: false, onDelete: 'CASCADE')]
    public Brand $brand;

    #[ORM\Column(length: 255)]
    public ?string $currency = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    public ?int $minimumGuaranteeMin = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    public ?int $minimumGuaranteeMax = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 8, scale: 5, nullable: true)]
    public ?string $royaltyRateMin = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 8, scale: 5, nullable: true)]
    public ?string $royaltyRateMax = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    public ?\DateTimeInterface $launchDate = null;

    /**
     * @var Collection<int, Territory>
     */
    #[ORM\ManyToMany(targetEntity: Territory::class)]
    public Collection $territories;

    /**
     * @var Collection<int, AgeGroup>
     */
    #[ORM\ManyToMany(targetEntity: AgeGroup::class)]
    public Collection $targetAudienceAgeGroups;

    /**
     * @var Collection<int, Gender>
     */
    #[ORM\ManyToMany(targetEntity: Gender::class)]
    public Collection $targetAudienceGenders;

    /**
     * @var Collection<int, ProductCategory>
     */
    #[ORM\ManyToMany(targetEntity: ProductCategory::class)]
    public Collection $productCategories;

    /**
     * @var Collection<int, DistributionChannel>
     */
    #[ORM\ManyToMany(targetEntity: DistributionChannel::class)]
    public Collection $distributionChannels;

    /**
     * @var Collection<int, ProjectBid>
     */
    #[ORM\OneToMany(targetEntity: ProjectBid::class, mappedBy: 'project')]
    public Collection $bids;

    public function __construct()
    {
        $this->territories = new ArrayCollection();
        $this->targetAudienceAgeGroups = new ArrayCollection();
        $this->targetAudienceGenders = new ArrayCollection();
        $this->productCategories = new ArrayCollection();
        $this->distributionChannels = new ArrayCollection();
        $this->bids = new ArrayCollection();
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getHeroImage(): ?string
    {
        return $this->brand->heroImage;
    }

    public Collection $ipCategories {
        get => new ArrayCollection();
    }

}
