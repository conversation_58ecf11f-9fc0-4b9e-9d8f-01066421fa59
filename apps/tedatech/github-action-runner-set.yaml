---
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: github-actions-runner
spec:
  refreshInterval: 1h
  target:
    name: github-actions-runner
    deletionPolicy: Delete
  secretStoreRef:
    kind: ClusterSecretStore
    name: github-actions-runner-secrets
  dataFrom:
    - find:
        name:
          regexp: ".*"
---
apiVersion: helm.toolkit.fluxcd.io/v2
kind: HelmRelease
metadata:
  name: arc-runner-scale-set
spec:
  interval: 10m
  timeout: 5m
  chartRef:
    kind: OCIRepository
    name: gha-runner-scale-set
    namespace: arc-systems
  driftDetection:
    mode: enabled
  install:
    crds: CreateReplace
    disableWait: true
    timeout: 10m
    remediation:
      retries: -1
  upgrade:
    disableWait: true
    force: true
    remediation:
      retries: -1
  values:
    runnerScaleSetName: tedatech
    githubConfigUrl: "https://github.com/TedaTech"
    githubConfigSecret: "github-actions-runner"
    
    maxRunners: 5
    minRunners: 2
    runnerGroup: default

    template:
      spec:
        securityContext:
          fsGroup: 1001
        imagePullSecrets:
          - name: github-pull-secret
        initContainers:
          - name: init-dind-externals
            image: ghcr.io/actions/actions-runner:latest
            imagePullPolicy: IfNotPresent
            command: [ "cp", "-r", "/home/<USER>/externals/.", "/home/<USER>/tmpDir/" ]
            volumeMounts:
              - name: dind-externals
                mountPath: /home/<USER>/tmpDir
          - name: init-nix-cache
            image: busybox:latest
            command:
              - sh
              - -c
              - |
                set -e
                chown -vf 1001:1001 /home/<USER>/_cache
            volumeMounts:
              - name: xdg-cache
                mountPath: /home/<USER>/_cache
        containers:
          - name: runner
            image: ghcr.io/tedatech/gha-runner:2.324.0-latest@sha256:164e9e11602ec4efb8890742ea6c46a907c31e41c172227f85a950f47c3f639a
            imagePullPolicy: IfNotPresent
            command: [ "/home/<USER>/run.sh" ]
            env:
              - name: DOCKER_HOST
                value: unix:///var/run/docker.sock
              - name: XDG_CACHE_HOME
                value: /home/<USER>/_cache
            volumeMounts:
              - name: work
                mountPath: /home/<USER>/_work
              - name: dind-sock
                mountPath: /var/run
              - name: xdg-cache
                mountPath: /home/<USER>/_cache
          - name: dind
            image: docker:dind
            args:
              - dockerd
              - --host=unix:///var/run/docker.sock
              - --group=$(DOCKER_GROUP_GID)
            env:
              - name: DOCKER_GROUP_GID
                value: "123"
            securityContext:
              privileged: true
            volumeMounts:
              - name: work
                mountPath: /home/<USER>/_work
              - name: dind-sock
                mountPath: /var/run
              - name: dind-externals
                mountPath: /home/<USER>/externals
        volumes:
          - name: work
            emptyDir: { }
          - name: xdg-cache
            emptyDir: { }
          - name: dind-sock
            emptyDir: { }
          - name: dind-externals
            emptyDir: { }
