<?php

namespace App\Repository;

use App\Entity\Licensor;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Licensor>
 */
class LicensorRepository extends ServiceEntityRepository
{

    public function __construct(
        ManagerRegistry $registry,
    ) {
        parent::__construct($registry, Licensor::class);
    }

    public function getByCompanySlug(string $companySlug): ?Licensor
    {
        return $this->createQueryBuilder('licensor')
            ->innerJoin('licensor.company', 'company')
            ->andWhere('company.slug = :slug')
            ->setParameter('slug', $companySlug)
            ->getQuery()
            ->getOneOrNullResult();
    }

}
