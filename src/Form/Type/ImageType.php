<?php

declare(strict_types=1);

namespace App\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Image;
use Symfony\UX\Dropzone\Form\DropzoneType;

/**
 * Use this for default image file constraints.
 */
class ImageType extends AbstractType
{

    public function getParent()
    {
        return DropzoneType::class;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'attr' => [
                'placeholder' => 'Drag and drop or browse',
            ],
            'constraints' => [
                new Image([
                    'maxSize' => '5M',
                    'extensionsMessage' => 'Please upload a valid image file (png, jpg, jpeg, svg, webp).',
                    'maxSizeMessage' => 'The file is too large. Maximum size allowed is 5 megabyte',
                    'disallowEmptyMessage' => true,
                    'filenameMaxLength' => 255,
                    'filenameTooLongMessage' => 'The file name is too long. Maximum length allowed is 255 characters.',
                ]),
            ],
        ]);
    }

}
